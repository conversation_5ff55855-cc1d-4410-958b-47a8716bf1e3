# AI Prompt Library

A comprehensive platform for discovering, sharing, and managing AI prompts across multiple platforms including ChatGPT, GitHub Copilot, Midjourney, and more.

## Features

### Core Features
- **Multi-Platform Support**: Prompts for ChatGPT, GitHub Copilot, Midjourney, and more
- **User Authentication**: Secure signup/login with role-based access
- **Prompt Management**: Create, edit, and organize prompts with categories
- **Search & Filter**: Advanced search with platform and category filters
- **Rating System**: Upvote/downvote prompts to help others discover quality content
- **Verification System**: Admin verification for prompt quality and safety

### Admin Features
- **Dashboard**: Comprehensive analytics and platform overview
- **User Management**: Manage users and assign roles (admin/moderator/user)
- **Prompt Moderation**: Review and verify submitted prompts
- **Category Management**: Organize prompts into categories
- **Bulk Upload**: Import prompts via Excel/CSV files
- **Featured Prompts**: Set top 4 prompts to display on the main page

### Featured Prompts System
The platform includes a featured prompts system that allows administrators to:

1. **Select Top Prompts**: Choose up to 4 high-quality prompts to showcase
2. **Main Page Display**: Featured prompts appear prominently on the homepage
3. **Easy Management**: Admin interface to add/remove featured prompts
4. **Quality Control**: Only verified prompts can be featured

#### How to Use Featured Prompts:
1. Navigate to Admin Dashboard → Featured Prompts
2. Select up to 4 verified prompts from the available list
3. Save changes to update the main page display
4. Featured prompts will appear in a dedicated section on the homepage

## Technology Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, MongoDB
- **Authentication**: NextAuth.js
- **Database**: MongoDB with Mongoose ODM
- **Styling**: Tailwind CSS with custom design system

## Getting Started

### Prerequisites
- Node.js 18+ 
- MongoDB database
- NextAuth.js configuration

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ai-prompt-library
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your environment variables:
```env
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Database Schema

### Prompt Model
```typescript
interface Prompt {
  title: string;
  description: string;
  prompt_text: string;
  instructions?: string;
  example?: string;
  category: ObjectId;
  platform: string;
  tags: string[];
  ratings: { user: ObjectId; value: number }[];
  created_by: ObjectId;
  verified: boolean;
  featured: boolean; // New field for featured prompts
  createdAt: Date;
  updatedAt: Date;
}
```

## API Endpoints

### Featured Prompts
- `GET /api/featured-prompts` - Get featured prompts for main page
- `GET /api/admin/featured-prompts` - Admin: Get all featured prompts
- `POST /api/admin/featured-prompts` - Admin: Update individual prompt featured status
- `PUT /api/admin/featured-prompts` - Admin: Set featured prompts (up to 4)

### Other Endpoints
- `GET /api/prompts` - Get prompts with filtering and pagination
- `POST /api/prompts` - Create new prompt
- `GET /api/admin/dashboard` - Admin dashboard statistics
- `GET /api/categories` - Get all categories
- `GET /api/platforms` - Get supported platforms

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License. 