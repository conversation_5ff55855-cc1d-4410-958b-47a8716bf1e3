/**
 * Enhanced logging utility for better error tracking
 */

export interface LogContext {
  userId?: string;
  requestId?: string;
  endpoint?: string;
  method?: string;
  userAgent?: string;
  ip?: string;
  [key: string]: any;
}

export class Logger {
  private static formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? ` | Context: ${JSON.stringify(context)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${contextStr}`;
  }

  static info(message: string, context?: LogContext): void {
    console.log(this.formatMessage('info', message, context));
  }

  static warn(message: string, context?: LogContext): void {
    console.warn(this.formatMessage('warn', message, context));
  }

  static error(message: string, error?: any, context?: LogContext): void {
    const errorDetails = error ? {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
    } : undefined;

    const fullContext = { ...context, error: errorDetails };
    console.error(this.formatMessage('error', message, fullContext));
  }

  static debug(message: string, context?: LogContext): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(this.formatMessage('debug', message, context));
    }
  }
}

/**
 * Extract request context for logging
 */
export function getRequestContext(req: any): LogContext {
  return {
    method: req.method,
    endpoint: req.url,
    userAgent: req.headers['user-agent'],
    ip: req.headers['x-forwarded-for'] || req.connection?.remoteAddress,
    requestId: req.headers['x-request-id'] || Math.random().toString(36).substring(7),
  };
}
