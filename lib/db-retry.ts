/**
 * Utility function to retry database operations with exponential backoff
 */
export async function retryDbOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      console.warn(`Database operation failed (attempt ${attempt}/${maxRetries}):`, {
        error: lastError.message,
        attempt,
        maxRetries
      });

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  if (!error) return false;

  const retryableErrors = [
    'MongoNetworkError',
    'MongoTimeoutError',
    'MongoServerSelectionError',
    'ECONNRESET',
    'ENOTFOUND',
    'ETIMEDOUT'
  ];

  return retryableErrors.some(errorType => 
    error.name === errorType || 
    error.code === errorType ||
    (error.message && error.message.includes(errorType))
  );
}
