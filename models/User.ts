import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface I<PERSON>ser extends Document {
  email: string;
  name: string;
  password: string;
  role: 'user' | 'admin' | 'moderator' | 'super admin';
  bio?: string;
  preferences?: {
    emailNotifications: boolean;
    publicProfile: boolean;
    defaultPlatform: 'ChatGPT' | 'GitHub Copilot' | 'Midjourney';
  };
  activity_log: { action: string; timestamp: Date }[];
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['user', 'admin', 'moderator', 'super admin'], default: 'user' },
  bio: { type: String, default: '' },
  preferences: {
    emailNotifications: { type: Boolean, default: true },
    publicProfile: { type: Boolean, default: false },
    defaultPlatform: { type: String, enum: ['ChatGPT', 'GitHub Copilot', 'Midjourney'], default: 'ChatGPT' }
  },
  activity_log: [
    {
      action: String,
      timestamp: Date,
    },
  ],
}, { timestamps: true });

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Compare password method
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema); 