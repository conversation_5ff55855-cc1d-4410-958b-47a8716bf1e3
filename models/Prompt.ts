import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IPrompt extends Document {
  title: string;
  description: string;
  prompt_text: string;
  instructions?: string;
  example?: string;
  category: Types.ObjectId;
  platform: string;
  tags: string[];
  ratings: { user: Types.ObjectId; value: number }[];
  created_by: Types.ObjectId;
  verified: boolean;
  featured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const PromptSchema = new Schema<IPrompt>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  prompt_text: { type: String, required: true },
  instructions: { type: String, required: false },
  example: { type: String, required: false },
  category: { type: Schema.Types.ObjectId, ref: 'Category', required: true },
  platform: { type: String, required: true },
  tags: [{ type: String }],
  ratings: [
    {
      user: { type: Schema.Types.ObjectId, ref: 'User' },
      value: { type: Number, enum: [1, -1] },
    },
  ],
  created_by: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  verified: { type: Boolean, default: false },
  featured: { type: Boolean, default: false },
}, { timestamps: true });

export default mongoose.models.Prompt || mongoose.model<IPrompt>('Prompt', PromptSchema); 