import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IFavorite extends Document {
  user: Types.ObjectId;
  prompt: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const FavoriteSchema = new Schema<IFavorite>({
  user: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  prompt: { 
    type: Schema.Types.ObjectId, 
    ref: 'Prompt', 
    required: true 
  },
}, { timestamps: true });

// Create compound index to ensure a user can only favorite a prompt once
FavoriteSchema.index({ user: 1, prompt: 1 }, { unique: true });

// Index for efficient queries
FavoriteSchema.index({ user: 1, createdAt: -1 });
FavoriteSchema.index({ prompt: 1 });

export default mongoose.models.Favorite || mongoose.model<IFavorite>('Favorite', FavoriteSchema);
