import mongoose, { Schema, Document } from 'mongoose';

export interface IPlatform extends Document {
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

const PlatformSchema = new Schema<IPlatform>({
  name: { 
    type: String, 
    required: true, 
    unique: true,
    trim: true
  },
}, { timestamps: true });

export default mongoose.models.Platform || mongoose.model<IPlatform>('Platform', PlatformSchema);
