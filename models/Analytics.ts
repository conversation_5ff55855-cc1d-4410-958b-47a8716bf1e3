import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IAnalytics extends Document {
  prompt_views: { prompt: Types.ObjectId; user: Types.ObjectId; timestamp: Date }[];
  search_queries: { query: string; user: Types.ObjectId; timestamp: Date }[];
  rating_activities: { prompt: Types.ObjectId; user: Types.ObjectId; value: number; timestamp: Date }[];
  createdAt: Date;
  updatedAt: Date;
}

const AnalyticsSchema = new Schema<IAnalytics>({
  prompt_views: [
    {
      prompt: { type: Schema.Types.ObjectId, ref: 'Prompt' },
      user: { type: Schema.Types.ObjectId, ref: 'User' },
      timestamp: { type: Date, default: Date.now },
    },
  ],
  search_queries: [
    {
      query: String,
      user: { type: Schema.Types.ObjectId, ref: 'User' },
      timestamp: { type: Date, default: Date.now },
    },
  ],
  rating_activities: [
    {
      prompt: { type: Schema.Types.ObjectId, ref: 'Prompt' },
      user: { type: Schema.Types.ObjectId, ref: 'User' },
      value: Number,
      timestamp: { type: Date, default: Date.now },
    },
  ],
}, { timestamps: true });

export default mongoose.models.Analytics || mongoose.model<IAnalytics>('Analytics', AnalyticsSchema); 