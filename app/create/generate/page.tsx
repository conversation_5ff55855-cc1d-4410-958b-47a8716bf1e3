'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  ChevronLeft, 
  ChevronRight, 
  Sparkles, 
  Target, 
  Settings, 
  ArrowUpDown, 
  Eye, 
  Save,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface GenerateFormData {
  // Step 1: Purpose and Goal
  purpose: string;
  goal: string;
  audience: string;
  
  // Step 2: Platform and Context
  platform: string;
  context: string;
  tone: string;
  
  // Step 3: Input and Output
  inputType: string;
  inputExample: string;
  outputFormat: string;
  outputExample: string;
  
  // Step 4: Generated content
  generatedPrompt: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
}

const STEPS = [
  { id: 1, title: 'Purpose & Goal', icon: Target, description: 'Define what you want to accomplish' },
  { id: 2, title: 'Platform & Context', icon: Settings, description: 'Choose platform and set context' },
  { id: 3, title: 'Input & Output', icon: ArrowUpDown, description: 'Define inputs and expected outputs' },
  { id: 4, title: 'Generate & Review', icon: Sparkles, description: 'AI generates your prompt' },
  { id: 5, title: 'Save & Submit', icon: Save, description: 'Approve and save your prompt' },
];

export default function GeneratePromptPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<any[]>([]);

  const [formData, setFormData] = useState<GenerateFormData>({
    purpose: '',
    goal: '',
    audience: '',
    platform: '',
    context: '',
    tone: '',
    inputType: '',
    inputExample: '',
    outputFormat: '',
    outputExample: '',
    generatedPrompt: '',
    title: '',
    description: '',
    category: '',
    tags: [],
  });

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin?callbackUrl=/create/generate');
      return;
    }

    loadCategories();
  }, [session, status, router]);

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (err) {
      console.error('Failed to load categories:', err);
    }
  };

  const updateFormData = (field: keyof GenerateFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const generatePrompt = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/prompts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          purpose: formData.purpose,
          goal: formData.goal,
          audience: formData.audience,
          platform: formData.platform,
          context: formData.context,
          tone: formData.tone,
          inputType: formData.inputType,
          inputExample: formData.inputExample,
          outputFormat: formData.outputFormat,
          outputExample: formData.outputExample,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate prompt');
      }

      const data = await response.json();

      updateFormData('generatedPrompt', data.prompt_text);
      updateFormData('title', data.title);
      updateFormData('description', data.description);
      updateFormData('tags', data.tags);
    } catch (err) {
      setError('Failed to generate prompt. Please try again.');
      console.error('Generate prompt error:', err);
    } finally {
      setLoading(false);
    }
  };

  const submitPrompt = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description,
          prompt_text: formData.generatedPrompt,
          instructions: `Generated prompt for ${formData.purpose}. Use with ${formData.platform}.`,
          example: formData.inputExample || formData.outputExample || '',
          platform: formData.platform,
          category: formData.category,
          tags: formData.tags,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save prompt');
      }

      const data = await response.json();

      // Redirect to the created prompt
      router.push(`/prompts/${data._id}`);
    } catch (err) {
      setError('Failed to save prompt. Please try again.');
      console.error('Submit prompt error:', err);
    } finally {
      setLoading(false);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return formData.purpose && formData.goal && formData.audience;
      case 2:
        return formData.platform && formData.context && formData.tone;
      case 3:
        return formData.inputType && formData.outputFormat;
      case 4:
        return formData.generatedPrompt && formData.title && formData.description;
      case 5:
        return formData.category;
      default:
        return false;
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-surface to-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-surface to-background">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gradient mb-4">
            Generate Your AI Prompt
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Let our AI help you create the perfect prompt through a guided step-by-step process
          </p>
        </div>

        {/* Progress Steps */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-between">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = currentStep > step.id;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex flex-col items-center ${index < STEPS.length - 1 ? 'flex-1' : ''}`}>
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                      isCompleted 
                        ? 'bg-primary border-primary text-primary-foreground' 
                        : isActive 
                        ? 'border-primary text-primary bg-primary/10' 
                        : 'border-border text-muted-foreground'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="w-6 h-6" />
                      ) : (
                        <Icon className="w-6 h-6" />
                      )}
                    </div>
                    <div className="text-center mt-2">
                      <p className={`text-sm font-medium ${isActive ? 'text-primary' : 'text-muted-foreground'}`}>
                        {step.title}
                      </p>
                      <p className="text-xs text-muted-foreground hidden sm:block">
                        {step.description}
                      </p>
                    </div>
                  </div>
                  
                  {index < STEPS.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-primary' : 'bg-border'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-card rounded-xl shadow-lg border border-border p-8">
            {error && (
              <div className="mb-6 p-4 bg-destructive-light border border-destructive rounded-lg flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-destructive" />
                <span className="text-destructive">{error}</span>
              </div>
            )}

            {/* Step Content */}
            <div className="mb-8">
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Target className="w-12 h-12 text-primary mx-auto mb-3" />
                    <h3 className="text-2xl font-semibold mb-2">What's Your Purpose?</h3>
                    <p className="text-muted-foreground">Help us understand what you want to accomplish with your AI prompt</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        What do you want to use this prompt for? *
                      </label>
                      <select
                        value={formData.purpose}
                        onChange={(e) => updateFormData('purpose', e.target.value)}
                        className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                      >
                        <option value="">Select a purpose...</option>
                        <option value="content-creation">Content Creation</option>
                        <option value="code-assistance">Code Assistance</option>
                        <option value="data-analysis">Data Analysis</option>
                        <option value="creative-writing">Creative Writing</option>
                        <option value="problem-solving">Problem Solving</option>
                        <option value="learning-education">Learning & Education</option>
                        <option value="business-strategy">Business Strategy</option>
                        <option value="image-generation">Image Generation</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Describe your specific goal *
                      </label>
                      <textarea
                        value={formData.goal}
                        onChange={(e) => updateFormData('goal', e.target.value)}
                        placeholder="e.g., I want to create engaging social media posts for my business, or I need help debugging Python code..."
                        className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background resize-none"
                        rows={4}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Who is your target audience? *
                      </label>
                      <input
                        type="text"
                        value={formData.audience}
                        onChange={(e) => updateFormData('audience', e.target.value)}
                        placeholder="e.g., Software developers, Marketing professionals, Students, General public..."
                        className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Settings className="w-12 h-12 text-primary mx-auto mb-3" />
                    <h3 className="text-2xl font-semibold mb-2">Platform & Context</h3>
                    <p className="text-muted-foreground">Choose your AI platform and provide context for better prompt generation</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Which AI platform will you use? *
                      </label>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                        {categories.length > 0 ? (
                          [...new Set(categories.map(cat => cat.platform))].sort().map((platform) => (
                            <button
                              key={platform}
                              onClick={() => updateFormData('platform', platform)}
                              className={`p-3 border rounded-lg text-center transition-all duration-200 min-h-[70px] flex items-center justify-center ${
                                formData.platform === platform
                                  ? 'border-primary bg-primary/10 text-primary'
                                  : 'border-border hover:border-primary/50 hover:bg-surface-hover'
                              }`}
                            >
                              <div className="font-medium text-sm leading-tight break-words">{platform}</div>
                            </button>
                          ))
                        ) : (
                          // Loading state or fallback
                          ['ChatGPT', 'GitHub Copilot', 'Midjourney'].map((platform) => (
                            <button
                              key={platform}
                              onClick={() => updateFormData('platform', platform)}
                              className={`p-3 border rounded-lg text-center transition-all duration-200 min-h-[70px] flex items-center justify-center ${
                                formData.platform === platform
                                  ? 'border-primary bg-primary/10 text-primary'
                                  : 'border-border hover:border-primary/50 hover:bg-surface-hover'
                              }`}
                            >
                              <div className="font-medium text-sm leading-tight break-words">{platform}</div>
                            </button>
                          ))
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Provide additional context *
                      </label>
                      <textarea
                        value={formData.context}
                        onChange={(e) => updateFormData('context', e.target.value)}
                        placeholder="Describe your specific use case, industry, constraints, or any other relevant context that will help generate a better prompt..."
                        className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background resize-none"
                        rows={4}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        What tone should the AI use? *
                      </label>
                      <select
                        value={formData.tone}
                        onChange={(e) => updateFormData('tone', e.target.value)}
                        className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                      >
                        <option value="">Select a tone...</option>
                        <option value="professional">Professional</option>
                        <option value="casual">Casual</option>
                        <option value="friendly">Friendly</option>
                        <option value="formal">Formal</option>
                        <option value="creative">Creative</option>
                        <option value="technical">Technical</option>
                        <option value="conversational">Conversational</option>
                        <option value="authoritative">Authoritative</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <ArrowUpDown className="w-12 h-12 text-primary mx-auto mb-3" />
                    <h3 className="text-2xl font-semibold mb-2">Input & Output</h3>
                    <p className="text-muted-foreground">Define what information you'll provide and what you expect to receive</p>
                  </div>

                  <div className="space-y-6">
                    <div className="bg-surface/50 p-6 rounded-lg">
                      <h4 className="text-lg font-semibold mb-4 flex items-center">
                        <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm mr-3">1</span>
                        Input Information
                      </h4>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            What type of input will you provide? *
                          </label>
                          <select
                            value={formData.inputType}
                            onChange={(e) => updateFormData('inputType', e.target.value)}
                            className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                          >
                            <option value="">Select input type...</option>
                            <option value="text">Text/Description</option>
                            <option value="code">Code</option>
                            <option value="data">Data/Numbers</option>
                            <option value="image-description">Image Description</option>
                            <option value="topic">Topic/Subject</option>
                            <option value="question">Question</option>
                            <option value="requirements">Requirements/Specifications</option>
                            <option value="none">No specific input needed</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Example of input (optional)
                          </label>
                          <textarea
                            value={formData.inputExample}
                            onChange={(e) => updateFormData('inputExample', e.target.value)}
                            placeholder="Provide an example of what you might input..."
                            className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background resize-none"
                            rows={3}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-surface/50 p-6 rounded-lg">
                      <h4 className="text-lg font-semibold mb-4 flex items-center">
                        <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm mr-3">2</span>
                        Expected Output
                      </h4>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            What format should the output be in? *
                          </label>
                          <select
                            value={formData.outputFormat}
                            onChange={(e) => updateFormData('outputFormat', e.target.value)}
                            className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                          >
                            <option value="">Select output format...</option>
                            <option value="paragraph">Paragraph/Essay</option>
                            <option value="list">Bullet Points/List</option>
                            <option value="code">Code</option>
                            <option value="table">Table/Structured Data</option>
                            <option value="steps">Step-by-step Instructions</option>
                            <option value="creative">Creative Content</option>
                            <option value="analysis">Analysis/Report</option>
                            <option value="summary">Summary</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Example of desired output (optional)
                          </label>
                          <textarea
                            value={formData.outputExample}
                            onChange={(e) => updateFormData('outputExample', e.target.value)}
                            placeholder="Describe or show an example of what you want the AI to produce..."
                            className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background resize-none"
                            rows={3}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Sparkles className="w-12 h-12 text-primary mx-auto mb-3" />
                    <h3 className="text-2xl font-semibold mb-2">Generate & Review</h3>
                    <p className="text-muted-foreground">AI will generate your prompt based on your inputs</p>
                  </div>

                  {!formData.generatedPrompt ? (
                    <div className="text-center py-8">
                      <button
                        onClick={generatePrompt}
                        disabled={loading}
                        className="btn btn-primary btn-lg"
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                            Generating...
                          </>
                        ) : (
                          <>
                            <Sparkles className="w-5 h-5 mr-2" />
                            Generate My Prompt
                          </>
                        )}
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Generated Title
                        </label>
                        <input
                          type="text"
                          value={formData.title}
                          onChange={(e) => updateFormData('title', e.target.value)}
                          className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Description
                        </label>
                        <textarea
                          value={formData.description}
                          onChange={(e) => updateFormData('description', e.target.value)}
                          className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background resize-none"
                          rows={3}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Generated Prompt
                        </label>
                        <textarea
                          value={formData.generatedPrompt}
                          onChange={(e) => updateFormData('generatedPrompt', e.target.value)}
                          className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background resize-none font-mono text-sm"
                          rows={8}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          You can edit the generated prompt to better suit your needs
                        </p>
                      </div>

                      <div className="flex space-x-4">
                        <button
                          onClick={generatePrompt}
                          disabled={loading}
                          className="btn btn-outline btn-md"
                        >
                          {loading ? 'Regenerating...' : 'Regenerate'}
                        </button>
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(formData.generatedPrompt);
                            // You could add a toast notification here
                          }}
                          className="btn btn-outline btn-md"
                        >
                          Copy Prompt
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {currentStep === 5 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Save className="w-12 h-12 text-primary mx-auto mb-3" />
                    <h3 className="text-2xl font-semibold mb-2">Save & Submit</h3>
                    <p className="text-muted-foreground">Review and save your generated prompt to the library</p>
                  </div>

                  <div className="space-y-6">
                    {/* Prompt Preview */}
                    <div className="bg-surface/50 p-6 rounded-lg">
                      <h4 className="text-lg font-semibold mb-4">Prompt Preview</h4>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Title:</span>
                          <p className="text-foreground">{formData.title}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Description:</span>
                          <p className="text-foreground">{formData.description}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Platform:</span>
                          <p className="text-foreground">{formData.platform}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Tags:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {formData.tags.map((tag, index) => (
                              <span key={index} className="px-2 py-1 bg-primary/10 text-primary rounded-md text-sm">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Category Selection */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Select Category *
                      </label>
                      <select
                        value={formData.category}
                        onChange={(e) => updateFormData('category', e.target.value)}
                        className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                      >
                        <option value="">Choose a category...</option>
                        {categories
                          .filter(cat => cat.platform === formData.platform)
                          .map((category) => (
                            <option key={category._id} value={category._id}>
                              {category.name}
                            </option>
                          ))}
                      </select>
                    </div>

                    {/* Submit Button */}
                    <div className="text-center pt-4">
                      <button
                        onClick={submitPrompt}
                        disabled={loading || !formData.category}
                        className="btn btn-primary btn-lg disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="w-5 h-5 mr-2" />
                            Save to Library
                          </>
                        )}
                      </button>
                      <p className="text-sm text-muted-foreground mt-2">
                        Your prompt will be saved and available in the library
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                disabled={currentStep === 1}
                className="btn btn-outline btn-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </button>

              {currentStep === STEPS.length ? (
                <button
                  onClick={submitPrompt}
                  disabled={loading || !canProceed()}
                  className="btn btn-primary btn-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : 'Save Prompt'}
                  <Save className="w-4 h-4 ml-2" />
                </button>
              ) : (
                <button
                  onClick={nextStep}
                  disabled={!canProceed()}
                  className="btn btn-primary btn-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
