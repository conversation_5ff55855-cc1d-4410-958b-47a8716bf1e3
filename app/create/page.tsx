'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Save, Sparkles, AlertCircle, CheckCircle, Tag, Type, FileText, Layers, Hash } from 'lucide-react';

interface Category {
  _id: string;
  name: string;
  platform: string;
  description: string;
}

export default function CreatePromptPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [platforms, setPlatforms] = useState<string[]>(['ChatGPT', 'GitHub Copilot', 'Midjourney']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    prompt_text: '',
    instructions: '',
    example: '',
    platform: '',
    category: '',
    tags: '',
  });

  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin?callbackUrl=/create');
      return;
    }

    loadCategories();
  }, [session, status, router]);

  const loadCategories = async () => {
    try {
      // Load categories
      const categoriesResponse = await fetch('/api/categories');
      const categoriesData = await categoriesResponse.json();

      if (categoriesResponse.ok) {
        setCategories(categoriesData);
      } else {
        setError('Failed to load categories');
      }

      // Load platforms
      const platformsResponse = await fetch('/api/platforms');
      const platformsData = await platformsResponse.json();

      if (platformsResponse.ok) {
        setPlatforms(platformsData.platforms || ['ChatGPT', 'GitHub Copilot', 'Midjourney']);
      } else {
        console.error('Failed to load platforms:', platformsData.message);
        // Fallback to default platforms
        setPlatforms(['ChatGPT', 'GitHub Copilot', 'Midjourney']);
      }
    } catch (err) {
      setError('Failed to load data');
      console.error('Data loading error:', err);
      // Fallback to default platforms
      setPlatforms(['ChatGPT', 'GitHub Copilot', 'Midjourney']);
    }
  };

  const validateField = (name: string, value: string) => {
    const errors: {[key: string]: string} = {};

    switch (name) {
      case 'title':
        if (!value.trim()) errors.title = 'Title is required';
        else if (value.trim().length < 5) errors.title = 'Title must be at least 5 characters';
        else if (value.trim().length > 100) errors.title = 'Title must be less than 100 characters';
        break;
      case 'description':
        if (!value.trim()) errors.description = 'Description is required';
        else if (value.trim().length < 20) errors.description = 'Description must be at least 20 characters';
        else if (value.trim().length > 500) errors.description = 'Description must be less than 500 characters';
        break;
      case 'prompt_text':
        if (!value.trim()) errors.prompt_text = 'Prompt text is required';
        else if (value.trim().length < 10) errors.prompt_text = 'Prompt text must be at least 10 characters';
        break;
      case 'platform':
        if (!value) errors.platform = 'Platform is required';
        break;
      case 'category':
        if (!value) errors.category = 'Category is required';
        break;
    }

    return errors;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    // Reset category when platform changes
    if (name === 'platform') {
      setFormData(prev => ({
        ...prev,
        category: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setValidationErrors({});

    // Comprehensive validation
    const allErrors: {[key: string]: string} = {};
    Object.keys(formData).forEach(key => {
      const fieldErrors = validateField(key, formData[key as keyof typeof formData]);
      Object.assign(allErrors, fieldErrors);
    });

    if (Object.keys(allErrors).length > 0) {
      setValidationErrors(allErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      const tagsArray = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const promptData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        prompt_text: formData.prompt_text.trim(),
        instructions: formData.instructions.trim(),
        example: formData.example.trim(),
        platform: formData.platform,
        category: formData.category,
        tags: tagsArray,
        created_by: session?.user?.id,
        verified: false, // New prompts need admin verification
      };

      const response = await fetch('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(promptData),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        setFormData({
          title: '',
          description: '',
          prompt_text: '',
          instructions: '',
          example: '',
          platform: '',
          category: '',
          tags: '',
        });
        
        // Redirect to the created prompt after a short delay
        setTimeout(() => {
          router.push(`/prompts/${data._id}`);
        }, 2000);
      } else {
        setError(data.message || 'Failed to create prompt');
      }
    } catch (err) {
      setError('Failed to create prompt. Please try again.');
      console.error('Create prompt error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
          <span className="text-muted-foreground">Loading...</span>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const filteredCategories = categories.filter(cat =>
    !formData.platform || cat.platform === formData.platform
  );

  if (success) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="max-w-md w-full card p-8 text-center animate-scale-in">
          <div className="w-20 h-20 bg-success-light rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-success" />
          </div>
          <h2 className="text-2xl font-bold text-foreground mb-4">Prompt Created Successfully!</h2>
          <p className="text-muted-foreground mb-8 leading-relaxed">
            Your prompt has been submitted successfully and is pending admin review.
            You'll be notified once it's approved and published.
          </p>
          <div className="space-y-4">
            <Link
              href="/browse"
              className="btn btn-primary btn-lg w-full"
            >
              Browse Prompts
            </Link>
            <button
              onClick={() => {
                setSuccess(false);
                setFormData({
                  title: '',
                  description: '',
                  prompt_text: '',
                  instructions: '',
                  example: '',
                  platform: '',
                  category: '',
                  tags: '',
                });
                setValidationErrors({});
              }}
              className="btn btn-outline btn-lg w-full"
            >
              Create Another Prompt
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5 border-b border-border">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-responsive-2xl font-bold text-foreground mb-4">
              Create New Prompt
            </h1>
            <p className="text-responsive-base text-muted-foreground leading-relaxed">
              Share your AI prompt with the community. All submissions are reviewed before publication
              to ensure quality and relevance.
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-12">
        {error && (
          <div className="mb-8 card border-destructive bg-destructive-light p-6 animate-slide-down">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-destructive flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <div className="text-destructive font-medium">{error}</div>
                <button
                  onClick={() => setError(null)}
                  className="mt-2 text-sm text-destructive hover:text-destructive/80 underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="card p-8 max-w-4xl mx-auto">
          <div className="space-y-8">
            {/* Title */}
            <div>
              <label htmlFor="title" className="flex items-center text-sm font-medium text-foreground mb-3">
                <Type className="w-4 h-4 mr-2 text-primary" />
                Prompt Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`input input-lg w-full ${validationErrors.title ? 'border-destructive focus:ring-destructive' : ''}`}
                placeholder="Enter a descriptive title for your prompt"
                required
              />
              {validationErrors.title && (
                <p className="mt-2 text-sm text-destructive flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {validationErrors.title}
                </p>
              )}
              <p className="mt-2 text-xs text-muted-foreground">
                {formData.title.length}/100 characters
              </p>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="flex items-center text-sm font-medium text-foreground mb-3">
                <FileText className="w-4 h-4 mr-2 text-primary" />
                Description *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={6}
                className={`input w-full resize-none ${validationErrors.description ? 'border-destructive focus:ring-destructive' : ''}`}
                placeholder="Describe what this prompt does, when to use it, and what makes it effective"
                required
              />
              {validationErrors.description && (
                <p className="mt-2 text-sm text-destructive flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {validationErrors.description}
                </p>
              )}
              <p className="mt-2 text-xs text-muted-foreground">
                {formData.description.length}/500 characters
              </p>
            </div>

            {/* Platform and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="platform" className="flex items-center text-sm font-medium text-foreground mb-3">
                  <Layers className="w-4 h-4 mr-2 text-primary" />
                  Platform *
                </label>
                <select
                  id="platform"
                  name="platform"
                  value={formData.platform}
                  onChange={handleInputChange}
                  className={`input w-full ${validationErrors.platform ? 'border-destructive focus:ring-destructive' : ''}`}
                  required
                >
                  <option value="">Select a platform</option>
                  {platforms.map(platform => (
                    <option key={platform} value={platform}>{platform}</option>
                  ))}
                </select>
                {validationErrors.platform && (
                  <p className="mt-2 text-sm text-destructive flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {validationErrors.platform}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="category" className="flex items-center text-sm font-medium text-foreground mb-3">
                  <Tag className="w-4 h-4 mr-2 text-primary" />
                  Category *
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={`input w-full ${validationErrors.category ? 'border-destructive focus:ring-destructive' : ''} ${!formData.platform ? 'opacity-50 cursor-not-allowed' : ''}`}
                  required
                  disabled={!formData.platform}
                >
                  <option value="">Select a category</option>
                  {filteredCategories.map(category => (
                    <option key={category._id} value={category._id}>{category.name}</option>
                  ))}
                </select>
                {validationErrors.category && (
                  <p className="mt-2 text-sm text-destructive flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {validationErrors.category}
                  </p>
                )}
                {!formData.platform && (
                  <p className="mt-2 text-xs text-muted-foreground">
                    Please select a platform first
                  </p>
                )}
              </div>
            </div>

            {/* Prompt Text */}
            <div>
              <label htmlFor="prompt_text" className="flex items-center text-sm font-medium text-foreground mb-3">
                <FileText className="w-4 h-4 mr-2 text-primary" />
                Prompt Text *
              </label>
              <textarea
                id="prompt_text"
                name="prompt_text"
                value={formData.prompt_text}
                onChange={handleInputChange}
                rows={20}
                className={`w-full px-4 py-4 border border-border rounded-lg bg-background text-foreground font-mono text-sm leading-relaxed resize-y min-h-[400px] focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors ${validationErrors.prompt_text ? 'border-destructive focus:ring-destructive' : ''}`}
                placeholder="Enter your prompt text here. Use [PLACEHOLDER] for variables that users should replace.

Example:
Write a [TYPE] about [TOPIC] for [AUDIENCE]. The tone should be [TONE] and include [SPECIFIC_REQUIREMENTS].

Make sure to:
- Be specific about the desired output format
- Include context when necessary
- Use clear, actionable language
- Test your prompt before submitting"
                required
              />
              {validationErrors.prompt_text && (
                <p className="mt-2 text-sm text-destructive flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {validationErrors.prompt_text}
                </p>
              )}
              <div className="mt-3 p-4 bg-primary-light rounded-lg">
                <p className="text-sm text-primary font-medium mb-2">💡 Pro Tips:</p>
                <ul className="text-sm text-primary space-y-1">
                  <li>• Use placeholders like [TOPIC], [AUDIENCE], or [CONTEXT] to make your prompt reusable</li>
                  <li>• Be specific about the desired output format and style</li>
                  <li>• Include examples when helpful</li>
                </ul>
              </div>
            </div>

            {/* Instructions */}
            <div>
              <label htmlFor="instructions" className="flex items-center text-sm font-medium text-foreground mb-3">
                <FileText className="w-4 h-4 mr-2 text-primary" />
                Usage Instructions (Optional)
              </label>
              <textarea
                id="instructions"
                name="instructions"
                value={formData.instructions}
                onChange={handleInputChange}
                rows={8}
                className="input w-full resize-none"
                placeholder="Provide clear instructions on how to use this prompt effectively. For example:

1. Replace [TOPIC] with your specific subject
2. Adjust the tone based on your audience
3. Add any specific requirements or constraints

This helps other users get the best results from your prompt."
              />
              <p className="mt-2 text-sm text-muted-foreground">
                Help others understand how to use your prompt effectively. Include tips for customizing placeholders and getting the best results.
              </p>
            </div>

            {/* Example */}
            <div>
              <label htmlFor="example" className="flex items-center text-sm font-medium text-foreground mb-3">
                <FileText className="w-4 h-4 mr-2 text-primary" />
                Example Usage (Optional)
              </label>
              <textarea
                id="example"
                name="example"
                value={formData.example}
                onChange={handleInputChange}
                rows={10}
                className="input w-full resize-none"
                placeholder="Show an example of your prompt in action. For instance:

Original prompt: 'Write a [TYPE] about [TOPIC] for [AUDIENCE]'

Example usage:
'Write a blog post about sustainable living for young professionals'

Expected output: A well-structured blog post that discusses sustainable living practices specifically tailored for busy young professionals, including practical tips they can implement in their daily lives."
              />
              <p className="mt-2 text-sm text-muted-foreground">
                Provide a concrete example showing how to use your prompt and what kind of output to expect. This helps users understand the prompt's potential.
              </p>
            </div>

            {/* Tags */}
            <div>
              <label htmlFor="tags" className="flex items-center text-sm font-medium text-foreground mb-3">
                <Hash className="w-4 h-4 mr-2 text-primary" />
                Tags (Optional)
              </label>
              <input
                type="text"
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                className="input w-full"
                placeholder="Enter tags separated by commas (e.g., writing, creative, blog, marketing)"
              />
              <p className="mt-2 text-sm text-muted-foreground">
                Add relevant tags to help others discover your prompt. Use 3-5 descriptive tags.
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-8 border-t border-border">
              <Link
                href="/browse"
                className="btn btn-outline btn-lg w-full sm:w-auto"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn btn-primary btn-lg w-full sm:w-auto min-w-[200px] relative"
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Creating...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Save className="w-4 h-4" />
                    <span>Create Prompt</span>
                  </div>
                )}
              </button>
            </div>
          </div>
        </form>

        {/* Enhanced Guidelines */}
        <div className="mt-12 card border-primary/20 bg-primary-light/50 p-8 max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center mr-4">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-foreground">Submission Guidelines</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-foreground mb-3">Quality Standards</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Make your prompt title clear and descriptive
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Provide a detailed description of what the prompt accomplishes
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Test your prompt before submitting to ensure it works well
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-foreground mb-3">Best Practices</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Use placeholders (e.g., [TOPIC]) for variables users should customize
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Add relevant tags to improve discoverability
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  All prompts are reviewed by moderators before publication
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
