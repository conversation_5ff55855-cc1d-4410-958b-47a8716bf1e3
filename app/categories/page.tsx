'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Category {
  _id: string;
  name: string;
  platform: string;
  description: string;
  promptCount?: number;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('All Platforms');

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/categories');
      const data = await response.json();
      
      if (response.ok) {
        // Get prompt counts for each category
        const categoriesWithCounts = await Promise.all(
          data.map(async (category: Category) => {
            try {
              const promptResponse = await fetch(`/api/prompts?category=${encodeURIComponent(category.name)}&limit=1`);
              const promptData = await promptResponse.json();
              return {
                ...category,
                promptCount: promptData.totalPrompts || 0
              };
            } catch {
              return { ...category, promptCount: 0 };
            }
          })
        );
        setCategories(categoriesWithCounts);
      } else {
        setError(data.message || 'Failed to load categories');
      }
    } catch (err) {
      setError('Failed to load categories');
      console.error('Categories error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading categories...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <button 
            onClick={loadCategories}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Group categories by platform
  const platforms = ['All Platforms', ...Array.from(new Set(categories.map(cat => cat.platform)))];
  const filteredCategories = selectedPlatform === 'All Platforms' 
    ? categories 
    : categories.filter(cat => cat.platform === selectedPlatform);

  const groupedCategories = filteredCategories.reduce((acc, category) => {
    if (!acc[category.platform]) {
      acc[category.platform] = [];
    }
    acc[category.platform].push(category);
    return acc;
  }, {} as Record<string, Category[]>);

  const platformColors = {
    'ChatGPT': 'bg-green-100 text-green-800 border-green-200',
    'GitHub Copilot': 'bg-blue-100 text-blue-800 border-blue-200',
    'Midjourney': 'bg-purple-100 text-purple-800 border-purple-200',
  };

  const platformIcons = {
    'ChatGPT': '🤖',
    'GitHub Copilot': '💻',
    'Midjourney': '🎨',
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Browse Categories
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore AI prompts organized by platform and category. Find the perfect prompts for your specific use case.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Platform Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap justify-center gap-4">
            {platforms.map((platform) => (
              <button
                key={platform}
                onClick={() => setSelectedPlatform(platform)}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  selectedPlatform === platform
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {platform !== 'All Platforms' && platformIcons[platform as keyof typeof platformIcons]} {platform}
              </button>
            ))}
          </div>
        </div>

        {/* Categories by Platform */}
        {selectedPlatform === 'All Platforms' ? (
          // Show all platforms grouped
          <div className="space-y-12">
            {Object.entries(groupedCategories).map(([platform, platformCategories]) => (
              <div key={platform}>
                <div className="flex items-center mb-6">
                  <div className={`inline-flex items-center px-4 py-2 rounded-lg border ${
                    platformColors[platform as keyof typeof platformColors] || 'bg-gray-100 text-gray-800 border-gray-200'
                  }`}>
                    <span className="mr-2 text-lg">
                      {platformIcons[platform as keyof typeof platformIcons] || '🔧'}
                    </span>
                    <span className="font-semibold">{platform}</span>
                  </div>
                  <div className="ml-4 text-sm text-gray-500">
                    {platformCategories.length} categories
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {platformCategories.map((category) => (
                    <CategoryCard key={category._id} category={category} />
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Show selected platform only
          <div>
            <div className="flex items-center justify-between mb-6">
              <div className={`inline-flex items-center px-4 py-2 rounded-lg border ${
                platformColors[selectedPlatform as keyof typeof platformColors] || 'bg-gray-100 text-gray-800 border-gray-200'
              }`}>
                <span className="mr-2 text-lg">
                  {platformIcons[selectedPlatform as keyof typeof platformIcons] || '🔧'}
                </span>
                <span className="font-semibold">{selectedPlatform}</span>
              </div>
              <div className="text-sm text-gray-500">
                {filteredCategories.length} categories
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCategories.map((category) => (
                <CategoryCard key={category._id} category={category} />
              ))}
            </div>
          </div>
        )}

        {filteredCategories.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">No categories found for the selected platform</div>
            <button 
              onClick={() => setSelectedPlatform('All Platforms')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              View All Categories
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

function CategoryCard({ category }: { category: Category }) {
  const platformColors = {
    'ChatGPT': 'bg-green-50 border-green-200',
    'GitHub Copilot': 'bg-blue-50 border-blue-200',
    'Midjourney': 'bg-purple-50 border-purple-200',
  };

  const platformTextColors = {
    'ChatGPT': 'text-green-700',
    'GitHub Copilot': 'text-blue-700',
    'Midjourney': 'text-purple-700',
  };

  return (
    <Link
      href={`/browse?category=${encodeURIComponent(category.name)}&platform=${encodeURIComponent(category.platform)}`}
      className={`block p-6 rounded-lg border-2 transition-all hover:shadow-md hover:scale-105 ${
        platformColors[category.platform as keyof typeof platformColors] || 'bg-gray-50 border-gray-200'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className={`text-lg font-semibold ${
          platformTextColors[category.platform as keyof typeof platformTextColors] || 'text-gray-700'
        }`}>
          {category.name}
        </h3>
        <span className="text-sm text-gray-500 bg-white px-2 py-1 rounded-full">
          {category.promptCount || 0} prompts
        </span>
      </div>
      
      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
        {category.description || 'Explore prompts in this category'}
      </p>
      
      <div className="flex items-center justify-between">
        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
          platformColors[category.platform as keyof typeof platformColors] || 'bg-gray-100'
        } ${platformTextColors[category.platform as keyof typeof platformTextColors] || 'text-gray-700'}`}>
          {category.platform}
        </span>
        <span className="text-blue-600 text-sm font-medium">
          Browse →
        </span>
      </div>
    </Link>
  );
}
