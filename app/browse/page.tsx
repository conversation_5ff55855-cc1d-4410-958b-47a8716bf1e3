'use client';
import { useEffect, useState, Suspense, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import PromptCard from '../components/PromptCard';
import { Search, Filter, Grid, List, ChevronDown, X, CheckCircle, SortAsc, SortDesc, Sparkles } from 'lucide-react';

interface Category {
  _id: string;
  name: string;
  platform: string;
}

interface Prompt {
  _id: string;
  title: string;
  description: string;
  platform: string;
  category: {
    _id: string;
    name: string;
  };
  tags: string[];
  ratings: Array<{ user: string; value: number }>;
  verified: boolean;
  created_by: {
    _id: string;
    name: string;
  };
  createdAt: string;
}

function BrowseContent() {
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [platforms, setPlatforms] = useState<string[]>(['All Platforms']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [platform, setPlatform] = useState('All Platforms');
  const [category, setCategory] = useState('All Categories');
  const [sortBy, setSortBy] = useState('newest');
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 9;



  // Initialize from URL parameters and fetch prompts
  useEffect(() => {
    if (!searchParams) return;

    const urlSearch = searchParams.get('search');
    const urlPlatform = searchParams.get('platform');
    const urlCategory = searchParams.get('category');

    if (urlSearch) {
      setSearchTerm(urlSearch);
    }
    if (urlPlatform) {
      setPlatform(urlPlatform);
    }
    if (urlCategory) {
      setCategory(urlCategory);
    }

    // Fetch prompts immediately after setting URL parameters
    const fetchPromptsFromURL = async () => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams();
        if (urlPlatform && urlPlatform !== 'All Platforms') params.append('platform', urlPlatform);
        if (urlCategory && urlCategory !== 'All Categories') params.append('category', urlCategory);
        if (urlSearch && urlSearch.trim()) params.append('search', urlSearch.trim());
        params.append('sort', sortBy);
        params.append('page', currentPage.toString());
        params.append('limit', itemsPerPage.toString());

        const response = await fetch(`/api/prompts?${params.toString()}`);
        const data = await response.json();

        if (response.ok) {
          setPrompts(data.prompts || data);
          setTotalPages(data.totalPages || 1);
        } else if (response.status === 400) {
          setError(data.message || 'Invalid search criteria');
          setPrompts([]);
          setTotalPages(1);
        } else {
          throw new Error(data.message || 'Failed to load prompts');
        }
      } catch (err) {
        setError('Failed to load prompts');
        console.error('Error loading prompts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPromptsFromURL();
  }, [searchParams, sortBy, currentPage, itemsPerPage]);



  // Load prompts when filters change
  useEffect(() => {
    const fetchPrompts = async () => {
      setLoading(true);
      setError(null);

      // Client-side validation for search term length
      if (searchTerm.trim() && searchTerm.trim().length < 2) {
        setError('Search term must be at least 2 characters long');
        setPrompts([]);
        setTotalPages(1);
        setLoading(false);
        return;
      }

      try {
        const params = new URLSearchParams();
        if (platform !== 'All Platforms') params.append('platform', platform);
        if (category !== 'All Categories') params.append('category', category);
        if (verifiedOnly) params.append('verified', 'true');
        if (searchTerm.trim()) params.append('search', searchTerm.trim());
        params.append('sort', sortBy);
        params.append('page', currentPage.toString());
        params.append('limit', itemsPerPage.toString());

        const response = await fetch(`/api/prompts?${params.toString()}`);
        const data = await response.json();

        if (response.ok) {
          setPrompts(data.prompts || data);
          setTotalPages(data.totalPages || 1);
        } else if (response.status === 400) {
          setError(data.message || 'Invalid search criteria');
          setPrompts([]);
          setTotalPages(1);
        } else {
          throw new Error(data.message || 'Failed to load prompts');
        }
      } catch (err) {
        setError('Failed to load prompts');
        console.error('Error loading prompts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, [platform, category, sortBy, verifiedOnly, searchTerm, currentPage, itemsPerPage]);



  // Load categories and platforms on mount
  useEffect(() => {
    // Load categories
    fetch('/api/categories')
      .then(res => res.json())
      .then(data => setCategories(data))
      .catch(err => console.error('Failed to load categories:', err));

    // Load platforms
    fetch('/api/platforms')
      .then(res => res.json())
      .then(data => {
        const allPlatforms = ['All Platforms', ...(data.platforms || ['ChatGPT', 'GitHub Copilot', 'Midjourney'])];
        setPlatforms(allPlatforms);
      })
      .catch(err => {
        console.error('Failed to load platforms:', err);
        // Fallback to default platforms
        setPlatforms(['All Platforms', 'ChatGPT', 'GitHub Copilot', 'Midjourney']);
      });
  }, []);

  const handleRating = (promptId: string, value: number) => {
    // Update the prompt in the local state
    setPrompts(prevPrompts =>
      prevPrompts.map(prompt => {
        if (prompt._id === promptId) {
          const existingRatingIndex = prompt.ratings.findIndex(
            r => r.user === session?.user?.id
          );

          const newRatings = [...prompt.ratings];
          if (existingRatingIndex !== -1) {
            newRatings[existingRatingIndex] = { user: session!.user.id, value };
          } else {
            newRatings.push({ user: session!.user.id, value });
          }

          return { ...prompt, ratings: newRatings };
        }
        return prompt;
      })
    );
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate minimum search length
    if (searchTerm.trim() && searchTerm.trim().length < 2) {
      setError('Search term must be at least 2 characters long');
      setPrompts([]); // Clear prompts to show empty state
      setTotalPages(1);
      return;
    }

    setCurrentPage(1); // Reset to first page when searching
    setError(null); // Clear any previous errors
  };

  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'title', label: 'Alphabetical' }
  ];

  const filteredCategories = categories.filter(cat =>
    platform === 'All Platforms' || cat.platform === platform
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5 border-b border-border">
        <div className="container-custom py-12">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="text-center lg:text-left">
              <h1 className="text-responsive-2xl font-bold text-foreground mb-4">
                Browse Prompts
              </h1>
              <p className="text-responsive-base text-muted-foreground max-w-2xl">
                Discover and explore AI prompts for various platforms. Find the perfect prompt to enhance your AI workflow.
              </p>
            </div>
            <div className="flex items-center justify-center lg:justify-end space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{prompts.length}</div>
                <div className="text-sm text-muted-foreground">Prompts Found</div>
              </div>
              <div className="w-px h-12 bg-border"></div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-surface-hover text-muted-foreground'
                  }`}
                  title="Grid view"
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'list'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-surface-hover text-muted-foreground'
                  }`}
                  title="List view"
                >
                  <List className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Enhanced Search and Filters */}
        <div className="card p-6 mb-8">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mb-6">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-muted-foreground" />
              </div>
              <input
                type="text"
                placeholder="Search prompts by title, description, or tags..."
                className="input input-lg pl-12 pr-32"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute inset-y-0 right-0 flex items-center space-x-2 pr-2">
                <button
                  type="button"
                  onClick={() => setShowFilters(!showFilters)}
                  className={`btn btn-ghost btn-sm ${showFilters ? 'bg-primary text-primary-foreground' : ''}`}
                >
                  <Filter className="w-4 h-4 mr-1" />
                  Filters
                  <ChevronDown className={`w-4 h-4 ml-1 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
                </button>
                <button
                  type="submit"
                  className="btn btn-primary btn-sm"
                >
                  Search
                </button>
              </div>
            </div>
          </form>

          {/* Enhanced Filters */}
          {showFilters && (
            <div className="border-t border-border pt-6 animate-slide-down">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-3">
                    Platform
                  </label>
                  <select
                    className="input w-full"
                    value={platform}
                    onChange={e => {
                      setPlatform(e.target.value);
                      setCategory('All Categories');
                      setCurrentPage(1);
                    }}
                  >
                    {platforms.map(p => (
                      <option key={p} value={p}>{p}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-3">
                    Category
                  </label>
                  <select
                    className="input w-full"
                    value={category}
                    onChange={e => {
                      setCategory(e.target.value);
                      setCurrentPage(1);
                    }}
                  >
                    <option value="All Categories">All Categories</option>
                    {filteredCategories.map(cat => (
                      <option key={cat._id} value={cat.name}>{cat.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-3">
                    Sort By
                  </label>
                  <select
                    className="input w-full"
                    value={sortBy}
                    onChange={e => {
                      setSortBy(e.target.value);
                      setCurrentPage(1);
                    }}
                  >
                    {sortOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-end">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      className="w-5 h-5 rounded border-border text-primary focus:ring-primary focus:ring-offset-0"
                      checked={verifiedOnly}
                      onChange={e => {
                        setVerifiedOnly(e.target.checked);
                        setCurrentPage(1);
                      }}
                    />
                    <span className="text-sm text-foreground flex items-center">
                      <CheckCircle className="w-4 h-4 mr-1 text-success" />
                      Verified only
                    </span>
                  </label>
                </div>
              </div>

              {/* Active Filters */}
              <div className="flex flex-wrap gap-3">
                {platform !== 'All Platforms' && (
                  <span className="badge badge-primary flex items-center space-x-1">
                    <span>Platform: {platform}</span>
                    <button
                      onClick={() => setPlatform('All Platforms')}
                      className="ml-1 hover:bg-primary-hover rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
                {category !== 'All Categories' && (
                  <span className="badge badge-accent flex items-center space-x-1">
                    <span>Category: {category}</span>
                    <button
                      onClick={() => setCategory('All Categories')}
                      className="ml-1 hover:bg-accent-hover rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
                {verifiedOnly && (
                  <span className="badge badge-secondary flex items-center space-x-1">
                    <CheckCircle className="w-3 h-3" />
                    <span>Verified only</span>
                    <button
                      onClick={() => setVerifiedOnly(false)}
                      className="ml-1 hover:bg-secondary-hover rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
                {searchTerm && (
                  <span className="badge badge-outline flex items-center space-x-1">
                    <span>Search: "{searchTerm}"</span>
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-1 hover:bg-surface-hover rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
              <span className="text-muted-foreground">Loading prompts...</span>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-20">
            <div className="max-w-md mx-auto">
              <div className="text-destructive mb-6 text-lg">{error}</div>
              <button
                onClick={() => window.location.reload()}
                className="btn btn-primary btn-lg"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : prompts.length === 0 ? (
          <div className="text-center py-20">
            <div className="max-w-md mx-auto">
              <Sparkles className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-foreground mb-4">No prompts found</h3>
              <div className="text-muted-foreground mb-6">
                {searchTerm ? (
                  <>
                    No prompts found for "<strong>{searchTerm}</strong>"
                    <div className="text-sm mt-2">Try different keywords or check your spelling</div>
                  </>
                ) : (
                  'No prompts match your current search criteria. Try adjusting your filters or search terms.'
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="btn btn-secondary btn-lg"
                  >
                    Clear Search
                  </button>
                )}
                <button
                  onClick={() => {
                    setPlatform('All Platforms');
                    setCategory('All Categories');
                    setVerifiedOnly(false);
                    setSearchTerm('');
                    setCurrentPage(1);
                  }}
                  className="btn btn-primary btn-lg"
                >
                  Clear All Filters
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Prompts Grid/List */}
            <div className={`mb-8 ${
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                : 'space-y-4'
            }`}>
              {prompts.map((prompt) => (
                <PromptCard
                  key={prompt._id}
                  prompt={prompt}
                  onRate={handleRating}
                />
              ))}
            </div>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="btn btn-outline btn-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                  let page;
                  if (totalPages <= 7) {
                    page = i + 1;
                  } else if (currentPage <= 4) {
                    page = i + 1;
                  } else if (currentPage >= totalPages - 3) {
                    page = totalPages - 6 + i;
                  } else {
                    page = currentPage - 3 + i;
                  }

                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`btn btn-md ${
                        currentPage === page
                          ? 'btn-primary'
                          : 'btn-outline'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="btn btn-outline btn-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default function BrowsePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
          <span className="text-muted-foreground">Loading browse page...</span>
        </div>
      </div>
    }>
      <BrowseContent />
    </Suspense>
  );
}