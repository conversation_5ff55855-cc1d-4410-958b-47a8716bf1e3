import './globals.css'
import { ReactNode } from 'react'
import Providers from './providers'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'AI Prompt Library - Discover & Share AI Prompts',
  description: 'Discover, share, and manage AI prompts for ChatGPT, GitHub Copilot, Midjourney, and more. Join our community of AI enthusiasts.',
  keywords: 'AI prompts, ChatGPT, GitHub Copilot, Midjourney, AI tools, prompt library',
  authors: [{ name: 'AI Prompt Library' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="min-h-screen bg-background font-sans antialiased flex flex-col">
        <Providers>
          <Navbar />
          <main className="pt-16 lg:pt-18 flex-1">
            {children}
          </main>
          <Footer />
        </Providers>
      </body>
    </html>
  )
}