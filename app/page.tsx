'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Search, Sparkles, ArrowRight, Star, Users, Zap, Globe, TrendingUp, BookOpen, Code, Image, MessageSquare, ExternalLink } from 'lucide-react';

interface FeaturedPrompt {
  _id: string;
  title: string;
  description: string;
  platform: string;
  category: {
    _id: string;
    name: string;
  };
  created_by: {
    _id: string;
    name: string;
  };
}

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [featuredPrompts, setFeaturedPrompts] = useState<FeaturedPrompt[]>([]);
  const [loadingFeatured, setLoadingFeatured] = useState(true);
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedQuery = searchQuery.trim();

    if (trimmedQuery) {
      // Validate minimum search length
      if (trimmedQuery.length < 2) {
        alert('Search term must be at least 2 characters long');
        return;
      }
      router.push(`/browse?search=${encodeURIComponent(trimmedQuery)}`);
    } else {
      router.push('/browse');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(e);
    }
  };

  // Load featured prompts
  useEffect(() => {
    const loadFeaturedPrompts = async () => {
      try {
        const response = await fetch('/api/featured-prompts');
        if (response.ok) {
          const data = await response.json();
          setFeaturedPrompts(data);
        }
      } catch (error) {
        console.error('Failed to load featured prompts:', error);
      } finally {
        setLoadingFeatured(false);
      }
    };

    loadFeaturedPrompts();
  }, []);

  const popularSearches = [
    { term: 'writing', icon: BookOpen, color: 'text-blue-600' },
    { term: 'code review', icon: Code, color: 'text-green-600' },
    { term: 'image generation', icon: Image, color: 'text-purple-600' },
    { term: 'analysis', icon: TrendingUp, color: 'text-orange-600' },
    { term: 'translation', icon: Globe, color: 'text-teal-600' },
    { term: 'chat', icon: MessageSquare, color: 'text-pink-600' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary-light/20 to-accent-light/20">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

        <div className="relative container-custom section-padding">
          <div className="text-center max-w-5xl mx-auto">
            {/* Main Heading */}
            <div className="mb-8 animate-fade-in">
              <h1 className="text-responsive-3xl font-bold mb-6 text-gradient leading-tight">
                AI Multi-Platform Prompt Library
              </h1>
              <p className="text-responsive-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Discover, share, and manage AI prompts for ChatGPT, GitHub Copilot, Midjourney, and more.
                Join our community of AI enthusiasts and boost your productivity with curated prompts.
              </p>
            </div>

            {/* Enhanced Search Section */}
            <div className="w-full max-w-3xl mx-auto mb-16 animate-slide-up">
              <form onSubmit={handleSearch} className="relative group">
                <div className="relative">
                  <div className={`absolute inset-0 bg-gradient-primary rounded-2xl blur-xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 ${isSearchFocused ? 'opacity-30' : ''}`}></div>
                  <div className="relative bg-card border border-border rounded-2xl shadow-xl overflow-hidden">
                    <div className="flex items-center">
                      <div className="pl-6">
                        <Search className={`w-6 h-6 transition-colors ${isSearchFocused ? 'text-primary' : 'text-muted-foreground'}`} />
                      </div>
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onFocus={() => setIsSearchFocused(true)}
                        onBlur={() => setIsSearchFocused(false)}
                        onKeyPress={handleKeyPress}
                        className="flex-1 px-4 py-6 text-lg bg-transparent border-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0 placeholder:text-muted-foreground"
                        placeholder="Search prompts... (e.g., 'writing', 'code review', 'image generation')"
                      />
                      <button
                        type="submit"
                        className="m-2 px-8 py-4 bg-gradient-primary text-white rounded-xl font-semibold hover:shadow-lg hover:scale-105 transition-all duration-200 flex items-center space-x-2"
                      >
                        <span>Search</span>
                        <ArrowRight className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </form>

              {/* Enhanced Quick Search Suggestions */}
              <div className="mt-8">
                <p className="text-sm text-muted-foreground mb-4">Popular searches:</p>
                <div className="flex flex-wrap justify-center gap-3">
                  {popularSearches.map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.term}
                        onClick={() => {
                          setSearchQuery(item.term);
                          router.push(`/browse?search=${encodeURIComponent(item.term)}`);
                        }}
                        className="flex items-center space-x-2 px-4 py-2 bg-card border border-border rounded-full hover:bg-surface-hover hover:border-primary/30 transition-all duration-200 hover:scale-105 group"
                      >
                        <Icon className={`w-4 h-4 ${item.color} group-hover:scale-110 transition-transform`} />
                        <span className="text-sm font-medium">{item.term}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Prompts Section */}
      {featuredPrompts.length > 0 && (
        <section className="py-16 bg-gradient-to-br from-yellow-50 via-white to-orange-50">
          <div className="container-custom">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <Star className="w-8 h-8 text-yellow-500 mr-3" />
                <h2 className="text-responsive-2xl font-bold text-gradient">
                  Featured Prompts
                </h2>
              </div>
              <p className="text-responsive-base text-muted-foreground max-w-2xl mx-auto">
                Discover our hand-picked top prompts that showcase the best of our community
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredPrompts.map((prompt, index) => (
                <div key={prompt._id} className="group bg-card border border-border rounded-xl p-6 hover:shadow-lg hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                      index === 1 ? 'bg-gradient-to-r from-gray-300 to-gray-400' :
                      index === 2 ? 'bg-gradient-to-r from-orange-400 to-orange-500' :
                      'bg-gradient-to-r from-blue-400 to-blue-500'
                    }`}>
                      {index + 1}
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      prompt.platform === 'ChatGPT' ? 'bg-green-100 text-green-800' :
                      prompt.platform === 'GitHub Copilot' ? 'bg-blue-100 text-blue-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {prompt.platform}
                    </span>
                  </div>

                  <h3 className="text-lg font-semibold text-foreground mb-3 line-clamp-2 group-hover:text-primary transition-colors">
                    {prompt.title}
                  </h3>

                  <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                    {prompt.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-surface-hover text-muted-foreground">
                      {prompt.category.name}
                    </span>
                    <Link
                      href={`/prompts/${prompt._id}`}
                      className="inline-flex items-center text-primary hover:text-primary-hover transition-colors text-sm font-medium"
                    >
                      View Prompt
                      <ExternalLink className="w-3 h-3 ml-1" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/browse"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-primary to-primary-hover hover:from-primary-hover hover:to-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Explore All Prompts
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}