'use client';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Star, ArrowLeft, CheckCircle, XCircle, Save, Loader2 } from 'lucide-react';

interface Prompt {
  _id: string;
  title: string;
  description: string;
  platform: string;
  category: {
    _id: string;
    name: string;
  };
  created_by: {
    _id: string;
    name: string;
  };
  verified: boolean;
  featured: boolean;
  createdAt: string;
}

export default function FeaturedPromptsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [featuredPrompts, setFeaturedPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (session.user?.role !== 'admin' && session.user?.role !== 'moderator') {
      router.push('/');
      return;
    }

    loadData();
  }, [session, status, router]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load all prompts
      const promptsResponse = await fetch('/api/prompts?limit=1000&verified=true');
      const promptsData = await promptsResponse.json();
      
      if (promptsResponse.ok) {
        setPrompts(promptsData.prompts || promptsData);
      } else {
        throw new Error(promptsData.message || 'Failed to load prompts');
      }

      // Load featured prompts
      const featuredResponse = await fetch('/api/admin/featured-prompts');
      const featuredData = await featuredResponse.json();
      
      if (featuredResponse.ok) {
        setFeaturedPrompts(featuredData);
      } else {
        throw new Error(featuredData.message || 'Failed to load featured prompts');
      }
    } catch (err) {
      setError('Failed to load data');
      console.error('Data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const toggleFeatured = (promptId: string) => {
    const isCurrentlyFeatured = featuredPrompts.some(p => p._id === promptId);
    
    if (isCurrentlyFeatured) {
      // Remove from featured
      setFeaturedPrompts(prev => prev.filter(p => p._id !== promptId));
    } else {
      // Add to featured (if less than 4)
      if (featuredPrompts.length >= 4) {
        setError('You can only feature up to 4 prompts. Please remove one first.');
        return;
      }
      
      const promptToAdd = prompts.find(p => p._id === promptId);
      if (promptToAdd) {
        setFeaturedPrompts(prev => [...prev, promptToAdd]);
      }
    }
    
    setError(null);
  };

  const saveFeaturedPrompts = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const promptIds = featuredPrompts.map(p => p._id);
      
      const response = await fetch('/api/admin/featured-prompts', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ promptIds }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Featured prompts updated successfully!');
        setFeaturedPrompts(data);
      } else {
        throw new Error(data.message || 'Failed to update featured prompts');
      }
    } catch (err) {
      setError('Failed to save featured prompts');
      console.error('Save error:', err);
    } finally {
      setSaving(false);
    }
  };

  const removeFromFeatured = (promptId: string) => {
    setFeaturedPrompts(prev => prev.filter(p => p._id !== promptId));
    setError(null);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading Featured Prompts...</h2>
        </div>
      </div>
    );
  }

  if (!session || (session.user?.role !== 'admin' && session.user?.role !== 'moderator')) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin"
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Link>
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                <Star className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                  Featured Prompts
                </h1>
                <p className="mt-1 text-gray-600">Manage the top 4 prompts displayed on the main page</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error/Success Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 text-red-400 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-400 mr-2" />
              <p className="text-green-800">{success}</p>
            </div>
          </div>
        )}

        {/* Featured Prompts Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Currently Featured ({featuredPrompts.length}/4)</h2>
              <p className="text-gray-600 mt-1">These prompts will be displayed on the main page</p>
            </div>
            <button
              onClick={saveFeaturedPrompts}
              disabled={saving}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-5 h-5 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>

          {featuredPrompts.length === 0 ? (
            <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8 text-center">
              <Star className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Featured Prompts</h3>
              <p className="text-gray-600">Select prompts from the list below to feature them on the main page.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {featuredPrompts.map((prompt, index) => (
                <div key={prompt._id} className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3 ${
                        index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                        index === 1 ? 'bg-gradient-to-r from-gray-300 to-gray-400' :
                        index === 2 ? 'bg-gradient-to-r from-orange-400 to-orange-500' :
                        'bg-gradient-to-r from-blue-400 to-blue-500'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{prompt.title}</h3>
                        <p className="text-sm text-gray-600">by {prompt.created_by.name}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFromFeatured(prompt._id)}
                      className="text-red-500 hover:text-red-700 transition-colors"
                      title="Remove from featured"
                    >
                      <XCircle className="w-5 h-5" />
                    </button>
                  </div>
                  
                  <p className="text-gray-700 mb-4 line-clamp-2">{prompt.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        prompt.platform === 'ChatGPT' ? 'bg-green-100 text-green-800' :
                        prompt.platform === 'GitHub Copilot' ? 'bg-blue-100 text-blue-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {prompt.platform}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {prompt.category.name}
                      </span>
                    </div>
                                         {prompt.verified && (
                       <CheckCircle className="w-5 h-5 text-green-500" />
                     )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Available Prompts Section */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Available Prompts</h2>
              <p className="text-gray-600 mt-1">Select prompts to feature (only verified prompts are shown)</p>
            </div>
            <div className="text-sm text-gray-500">
              {featuredPrompts.length}/4 selected
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {prompts
              .filter(prompt => !featuredPrompts.some(fp => fp._id === prompt._id))
              .map((prompt) => (
                <div key={prompt._id} className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{prompt.title}</h3>
                    <button
                      onClick={() => toggleFeatured(prompt._id)}
                      className="text-gray-400 hover:text-yellow-500 transition-colors ml-2"
                      title="Add to featured"
                    >
                      <Star className="w-5 h-5" />
                    </button>
                  </div>
                  
                  <p className="text-gray-700 mb-4 line-clamp-3">{prompt.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        prompt.platform === 'ChatGPT' ? 'bg-green-100 text-green-800' :
                        prompt.platform === 'GitHub Copilot' ? 'bg-blue-100 text-blue-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {prompt.platform}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {prompt.category.name}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      by {prompt.created_by.name}
                    </div>
                  </div>
                </div>
              ))}
          </div>

          {prompts.filter(prompt => !featuredPrompts.some(fp => fp._id === prompt._id)).length === 0 && (
            <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8 text-center">
              <p className="text-gray-600">No more verified prompts available to feature.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 