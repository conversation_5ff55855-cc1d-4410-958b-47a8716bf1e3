'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Prompt {
  _id: string;
  title: string;
  description: string;
  prompt_text: string;
  platform: string;
  category: {
    _id: string;
    name: string;
  };
  tags: string[];
  ratings: Array<{ user: string; value: number }>;
  verified: boolean;
  created_by: {
    _id: string;
    name: string;
  };
  createdAt: string;
}

export default function AdminPromptsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'verified' | 'pending'>('all');
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [categories, setCategories] = useState<Array<{_id: string; name: string; platform: string}>>([]);
  const [editFormData, setEditFormData] = useState({
    title: '',
    description: '',
    prompt_text: '',
    platform: '',
    category: '',
    tags: '',
    verified: false,
  });

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (session.user?.role !== 'admin') {
      router.push('/');
      return;
    }

    loadPrompts();
  }, [session, status, router]);

  const loadPrompts = async () => {
    try {
      setLoading(true);
      setError(null);

      const [promptsResponse, categoriesResponse] = await Promise.all([
        fetch('/api/admin/prompts'),
        fetch('/api/categories')
      ]);

      const [promptsData, categoriesData] = await Promise.all([
        promptsResponse.json(),
        categoriesResponse.json()
      ]);

      if (promptsResponse.ok) {
        setPrompts(promptsData);
      } else {
        setError(promptsData.message || 'Failed to load prompts');
      }

      if (categoriesResponse.ok) {
        setCategories(categoriesData);
      }
    } catch (err) {
      setError('Failed to load prompts');
      console.error('Prompts error:', err);
    } finally {
      setLoading(false);
    }
  };

  const verifyPrompt = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/prompts/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ verified: true }),
      });

      if (response.ok) {
        setPrompts(prompts.map(p => p._id === id ? { ...p, verified: true } : p));
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to verify prompt');
      }
    } catch (err) {
      setError('Failed to verify prompt');
    }
  };

  const deletePrompt = async (id: string) => {
    if (!confirm('Are you sure you want to delete this prompt? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/prompts/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setPrompts(prompts.filter(p => p._id !== id));
        setShowDetailModal(false);
        setSelectedPrompt(null);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete prompt');
      }
    } catch (err) {
      setError('Failed to delete prompt');
    }
  };

  const openEditModal = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setEditFormData({
      title: prompt.title,
      description: prompt.description,
      prompt_text: prompt.prompt_text,
      platform: prompt.platform,
      category: prompt.category._id,
      tags: prompt.tags.join(', '),
      verified: prompt.verified,
    });
    setShowEditModal(true);
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const savePromptEdit = async () => {
    if (!selectedPrompt) return;

    try {
      const updateData = {
        title: editFormData.title.trim(),
        description: editFormData.description.trim(),
        prompt_text: editFormData.prompt_text.trim(),
        platform: editFormData.platform,
        category: editFormData.category,
        tags: editFormData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        verified: editFormData.verified,
      };

      const response = await fetch(`/api/admin/prompts/${selectedPrompt._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const updatedPrompt = await response.json();
        setPrompts(prompts.map(p => p._id === selectedPrompt._id ? updatedPrompt : p));
        setShowEditModal(false);
        setSelectedPrompt(null);
        setError(null);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to update prompt');
      }
    } catch (err) {
      setError('Failed to update prompt');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading Prompts...</h2>
          <p className="text-gray-500 mt-2">Please wait while we fetch prompt data</p>
        </div>
      </div>
    );
  }

  if (!session || session.user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-6">You need admin privileges to access this page.</p>
          <Link href="/admin" className="text-blue-600 hover:text-blue-500">
            ← Back to Admin
          </Link>
        </div>
      </div>
    );
  }

  const filteredPrompts = prompts.filter(prompt => {
    if (filter === 'verified') return prompt.verified;
    if (filter === 'pending') return !prompt.verified;
    return true;
  });

  const calculateRating = (ratings: Array<{ value: number }>) => {
    return ratings.reduce((sum, rating) => sum + rating.value, 0);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/admin" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">Prompt Management</h1>
              <p className="mt-2 text-gray-600">Review and moderate user-submitted prompts</p>
            </div>
            <div className="text-sm text-gray-500">
              {filteredPrompts.length} of {prompts.length} prompts
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-600">{error}</div>
            <button
              onClick={() => setError(null)}
              className="mt-2 text-sm text-red-600 hover:text-red-800"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Filter Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'all', label: 'All Prompts', count: prompts.length },
                { key: 'pending', label: 'Pending Review', count: prompts.filter(p => !p.verified).length },
                { key: 'verified', label: 'Verified', count: prompts.filter(p => p.verified).length },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    filter === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Prompts Grid */}
        <div className="grid gap-6">
          {filteredPrompts.map((prompt) => (
            <div key={prompt._id} className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{prompt.title}</h3>
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">
                      {prompt.platform}
                    </span>
                    <span>{prompt.category.name}</span>
                    <span>By {prompt.created_by.name}</span>
                    <span>{new Date(prompt.createdAt).toLocaleDateString()}</span>
                  </div>
                  <p className="text-gray-700 mb-3">{prompt.description}</p>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {prompt.tags.map((tag) => (
                      <span key={tag} className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                        #{tag}
                      </span>
                    ))}
                  </div>
                  <div className="text-sm text-gray-500">
                    Rating: {calculateRating(prompt.ratings)} ({prompt.ratings.length} votes)
                  </div>
                </div>

                <div className="flex items-center gap-2 ml-4">
                  {prompt.verified ? (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      ✓ Verified
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      ⏳ Pending
                    </span>
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    setSelectedPrompt(prompt);
                    setShowDetailModal(true);
                  }}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View Details
                </button>

                <div className="flex items-center gap-3">
                  {!prompt.verified && (
                    <button
                      onClick={() => verifyPrompt(prompt._id)}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm font-medium"
                    >
                      Verify
                    </button>
                  )}
                  <button
                    onClick={() => openEditModal(prompt)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => deletePrompt(prompt._id)}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm font-medium"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredPrompts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">No prompts found for the selected filter</div>
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedPrompt && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-gray-900">{selectedPrompt.title}</h3>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-gray-700">{selectedPrompt.description}</p>
              </div>

              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Prompt Text</h4>
                <div className="bg-gray-50 rounded-lg p-4 border">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                    {selectedPrompt.prompt_text}
                  </pre>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                >
                  Close
                </button>
                {!selectedPrompt.verified && (
                  <button
                    onClick={() => {
                      verifyPrompt(selectedPrompt._id);
                      setShowDetailModal(false);
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Verify Prompt
                  </button>
                )}
                <button
                  onClick={() => deletePrompt(selectedPrompt._id)}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Delete Prompt
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && selectedPrompt && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Edit Prompt</h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form className="space-y-6">
                {/* Title */}
                <div>
                  <label htmlFor="edit-title" className="block text-sm font-medium text-gray-700 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    id="edit-title"
                    name="title"
                    value={editFormData.title}
                    onChange={handleEditInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="edit-description" className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    id="edit-description"
                    name="description"
                    value={editFormData.description}
                    onChange={handleEditInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                {/* Platform and Category */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="edit-platform" className="block text-sm font-medium text-gray-700 mb-2">
                      Platform
                    </label>
                    <select
                      id="edit-platform"
                      name="platform"
                      value={editFormData.platform}
                      onChange={handleEditInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="ChatGPT">ChatGPT</option>
                      <option value="GitHub Copilot">GitHub Copilot</option>
                      <option value="Midjourney">Midjourney</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="edit-category" className="block text-sm font-medium text-gray-700 mb-2">
                      Category
                    </label>
                    <select
                      id="edit-category"
                      name="category"
                      value={editFormData.category}
                      onChange={handleEditInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select a category</option>
                      {categories.filter(cat => cat.platform === editFormData.platform).map(category => (
                        <option key={category._id} value={category._id}>{category.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Prompt Text */}
                <div>
                  <label htmlFor="edit-prompt-text" className="block text-sm font-medium text-gray-700 mb-2">
                    Prompt Text
                  </label>
                  <textarea
                    id="edit-prompt-text"
                    name="prompt_text"
                    value={editFormData.prompt_text}
                    onChange={handleEditInputChange}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                    required
                  />
                </div>

                {/* Tags */}
                <div>
                  <label htmlFor="edit-tags" className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    id="edit-tags"
                    name="tags"
                    value={editFormData.tags}
                    onChange={handleEditInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter tags separated by commas"
                  />
                </div>

                {/* Verified Status */}
                <div className="flex items-center">
                  <input
                    id="edit-verified"
                    name="verified"
                    type="checkbox"
                    checked={editFormData.verified}
                    onChange={handleEditInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="edit-verified" className="ml-2 block text-sm text-gray-900">
                    Verified prompt
                  </label>
                </div>
              </form>

              <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={savePromptEdit}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}