'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { ThumbsUp, ThumbsDown, Eye, Copy, Calendar, User, Tag, CheckCircle, ExternalLink, Heart } from 'lucide-react';

interface PromptCardProps {
  prompt: {
    _id: string;
    title: string;
    description: string;
    platform: string;
    category: {
      _id: string;
      name: string;
    };
    tags: string[];
    ratings: Array<{ user: string; value: number }>;
    verified: boolean;
    created_by: {
      _id: string;
      name: string;
    };
    createdAt: string;
  };
  onRate?: (promptId: string, value: number) => void;
  onFavoriteChange?: (promptId: string, isFavorited: boolean) => void;
}

export default function PromptCard({ prompt, onRate, onFavoriteChange }: PromptCardProps) {
  const { data: session } = useSession();
  const [isRating, setIsRating] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [isFavoriting, setIsFavoriting] = useState(false);

  // Calculate rating statistics
  const upvotes = prompt.ratings.filter(r => r.value === 1).length;
  const downvotes = prompt.ratings.filter(r => r.value === -1).length;
  const totalRating = upvotes - downvotes;
  const userRating = session?.user?.id
    ? prompt.ratings.find(r => r.user === session.user.id)?.value
    : null;

  const handleRate = async (value: number) => {
    if (!session?.user?.id || isRating) return;

    setIsRating(true);
    try {
      const response = await fetch(`/api/prompts/${prompt._id}/rate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ value }),
      });

      if (response.ok && onRate) {
        onRate(prompt._id, value);
      }
    } catch (error) {
      console.error('Error rating prompt:', error);
    } finally {
      setIsRating(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(prompt.description);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  // Check if prompt is favorited on component mount
  useEffect(() => {
    const checkFavoriteStatus = async () => {
      if (!session?.user?.id) return;

      try {
        const response = await fetch(`/api/prompts/${prompt._id}/favorite`);
        if (response.ok) {
          const data = await response.json();
          setIsFavorited(data.isFavorited);
        }
      } catch (error) {
        console.error('Error checking favorite status:', error);
      }
    };

    checkFavoriteStatus();
  }, [session?.user?.id, prompt._id]);

  const handleFavorite = async () => {
    if (!session?.user?.id || isFavoriting) return;

    setIsFavoriting(true);
    try {
      const method = isFavorited ? 'DELETE' : 'POST';
      const response = await fetch(`/api/prompts/${prompt._id}/favorite`, {
        method,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        setIsFavorited(data.isFavorited);

        // Notify parent component of favorite change
        if (onFavoriteChange) {
          onFavoriteChange(prompt._id, data.isFavorited);
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    } finally {
      setIsFavoriting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPlatformColor = (platform: string) => {
    const colors: { [key: string]: string } = {
      'ChatGPT': 'bg-green-100 text-green-800 border-green-200',
      'GitHub Copilot': 'bg-blue-100 text-blue-800 border-blue-200',
      'Midjourney': 'bg-purple-100 text-purple-800 border-purple-200',
      'Claude': 'bg-orange-100 text-orange-800 border-orange-200',
      'Bard': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'Default': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[platform] || colors['Default'];
  };

  return (
    <div className="card card-interactive group overflow-hidden">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <Link href={`/prompts/${prompt._id}`} className="block group-hover:text-primary transition-colors">
              <h3 className="text-xl font-semibold text-foreground mb-2 line-clamp-2 leading-tight">
                {prompt.title}
              </h3>
            </Link>

            {/* Platform and Category */}
            <div className="flex items-center gap-3 mb-3">
              <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getPlatformColor(prompt.platform)}`}>
                {prompt.platform}
              </span>
              <div className="flex items-center text-sm text-muted-foreground">
                <Tag className="w-3 h-3 mr-1" />
                <span>{prompt.category.name}</span>
              </div>
              {prompt.verified && (
                <div className="flex items-center text-success text-sm font-medium">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  <span>Verified</span>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center space-x-2 ml-4">
            {session?.user?.id && (
              <button
                onClick={handleFavorite}
                disabled={isFavoriting}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  isFavorited
                    ? 'bg-red-100 text-red-600 hover:bg-red-200'
                    : 'hover:bg-surface-hover text-muted-foreground hover:text-red-500'
                } ${isFavoriting ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}`}
                title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
              >
                <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
              </button>
            )}
            <button
              onClick={handleCopy}
              className={`p-2 rounded-lg transition-all duration-200 ${
                isCopied
                  ? 'bg-success text-success-foreground'
                  : 'hover:bg-surface-hover text-muted-foreground hover:text-foreground'
              }`}
              title="Copy prompt"
            >
              {isCopied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
            </button>
            <Link
              href={`/prompts/${prompt._id}`}
              className="p-2 rounded-lg hover:bg-surface-hover text-muted-foreground hover:text-foreground transition-colors"
              title="View details"
            >
              <ExternalLink className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Description */}
        <p className="text-muted-foreground text-sm line-clamp-3 leading-relaxed mb-4">
          {prompt.description}
        </p>

        {/* Tags */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {prompt.tags.slice(0, 4).map((tag, index) => (
              <span
                key={index}
                className="badge badge-outline text-xs"
              >
                #{tag}
              </span>
            ))}
            {prompt.tags.length > 4 && (
              <span className="badge badge-secondary text-xs">
                +{prompt.tags.length - 4}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-surface/50 border-t border-border">
        <div className="flex items-center justify-between">
          {/* Author and Date */}
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <User className="w-3 h-3" />
              <span>{prompt.created_by.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(prompt.createdAt)}</span>
            </div>
          </div>

          {/* Rating System */}
          <div className="flex items-center space-x-3">
            {session?.user?.id ? (
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleRate(1)}
                  disabled={isRating}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    userRating === 1
                      ? 'bg-success text-success-foreground shadow-md'
                      : 'hover:bg-success-light text-muted-foreground hover:text-success'
                  } ${isRating ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}`}
                  title="Upvote"
                >
                  <ThumbsUp className="w-4 h-4" />
                </button>

                <div className="flex flex-col items-center mx-2">
                  <span className="text-sm font-semibold text-foreground">{totalRating}</span>
                  <span className="text-xs text-muted-foreground">score</span>
                </div>

                <button
                  onClick={() => handleRate(-1)}
                  disabled={isRating}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    userRating === -1
                      ? 'bg-destructive text-destructive-foreground shadow-md'
                      : 'hover:bg-destructive-light text-muted-foreground hover:text-destructive'
                  } ${isRating ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}`}
                  title="Downvote"
                >
                  <ThumbsDown className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <ThumbsUp className="w-4 h-4" />
                  <span className="text-sm">{upvotes}</span>
                </div>
                <div className="w-px h-4 bg-border"></div>
                <div className="flex items-center space-x-1">
                  <ThumbsDown className="w-4 h-4" />
                  <span className="text-sm">{downvotes}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}