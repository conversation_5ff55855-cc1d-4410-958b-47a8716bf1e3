export default function Footer() {
  return (
    <footer className="bg-background border-t border-border/30 py-6 mt-auto">
      <div className="container-custom">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-lg bg-gradient-primary flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                <path d="M20 3v4"></path>
                <path d="M22 5h-4"></path>
                <path d="M4 17v2"></path>
                <path d="M5 18H3"></path>
              </svg>
            </div>
            <span className="text-sm font-medium text-foreground">AI Prompt Library</span>
          </div>
          
          <div className="flex items-center space-x-6 text-sm text-muted-foreground">
            <span>© {new Date().getFullYear()} Omantel All rights reserved</span>
            <div className="hidden sm:flex items-center space-x-4">
              <a href="/browse" className="hover:text-foreground transition-colors">Browse</a>
              <a href="/categories" className="hover:text-foreground transition-colors">Categories</a>
              <a href="/create" className="hover:text-foreground transition-colors">Create</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 