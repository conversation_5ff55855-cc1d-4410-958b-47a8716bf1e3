'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';

interface Category {
  _id: string;
  name: string;
  platform: string;
}

interface Prompt {
  _id: string;
  title: string;
  description: string;
  prompt_text: string;
  instructions?: string;
  example?: string;
  platform: string;
  category: {
    _id: string;
    name: string;
  };
  tags: string[];
  verified: boolean;
  created_by: {
    _id: string;
    name: string;
  };
}

export default function EditPromptPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const promptId = params?.id as string;

  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    prompt_text: '',
    instructions: '',
    example: '',
    platform: 'ChatGPT',
    category: '',
    tags: ''
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin?callbackUrl=/my-prompts');
      return;
    }
  }, [session, status, router]);

  // Load prompt data
  useEffect(() => {
    if (!promptId || !session) return;

    const loadPrompt = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/prompts/${promptId}`);
        const data = await response.json();

        if (response.ok) {
          // Check if user owns this prompt
          if (data.created_by._id !== session.user?.id && session.user?.role !== 'admin') {
            setError('You can only edit your own prompts');
            return;
          }

          setPrompt(data);
          setFormData({
            title: data.title,
            description: data.description,
            prompt_text: data.prompt_text,
            instructions: data.instructions || '',
            example: data.example || '',
            platform: data.platform,
            category: data.category._id,
            tags: data.tags.join(', ')
          });
        } else {
          setError(data.message || 'Failed to load prompt');
        }
      } catch (err) {
        setError('Failed to load prompt');
        console.error('Error loading prompt:', err);
      } finally {
        setLoading(false);
      }
    };

    loadPrompt();
  }, [promptId, session]);

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        const data = await response.json();
        setCategories(data);
      } catch (err) {
        console.error('Failed to load categories:', err);
      }
    };

    loadCategories();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!promptId) return;

    try {
      setSaving(true);
      setError(null);

      // Prepare the data
      const updateData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        prompt_text: formData.prompt_text.trim(),
        instructions: formData.instructions.trim(),
        example: formData.example.trim(),
        platform: formData.platform,
        category: formData.category,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
      };

      const response = await fetch(`/api/prompts/${promptId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (response.ok) {
        router.push(`/prompts/${promptId}`);
      } else {
        setError(data.message || 'Failed to update prompt');
      }
    } catch (err) {
      setError('Failed to update prompt');
      console.error('Error updating prompt:', err);
    } finally {
      setSaving(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading...</span>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <button
            onClick={() => router.push('/my-prompts')}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to My Prompts
          </button>
        </div>
      </div>
    );
  }

  const filteredCategories = categories.filter(cat => 
    cat.platform === formData.platform
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Edit Prompt</h1>
          <p className="text-gray-600">
            Update your prompt details and content
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter a descriptive title for your prompt"
              />
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe what this prompt does and when to use it"
              />
            </div>

            {/* Platform */}
            <div>
              <label htmlFor="platform" className="block text-sm font-medium text-gray-700 mb-2">
                Platform *
              </label>
              <select
                id="platform"
                name="platform"
                value={formData.platform}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="ChatGPT">ChatGPT</option>
                <option value="GitHub Copilot">GitHub Copilot</option>
                <option value="Midjourney">Midjourney</option>
              </select>
            </div>

            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a category</option>
                {filteredCategories.map(category => (
                  <option key={category._id} value={category._id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Prompt Text */}
            <div>
              <label htmlFor="prompt_text" className="block text-sm font-medium text-gray-700 mb-2">
                Prompt Content *
              </label>
              <textarea
                id="prompt_text"
                name="prompt_text"
                value={formData.prompt_text}
                onChange={handleInputChange}
                required
                rows={8}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter the actual prompt text that users will copy and use"
              />
            </div>

            {/* Instructions */}
            <div>
              <label htmlFor="instructions" className="block text-sm font-medium text-gray-700 mb-2">
                Usage Instructions (Optional)
              </label>
              <textarea
                id="instructions"
                name="instructions"
                value={formData.instructions}
                onChange={handleInputChange}
                rows={6}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Provide clear instructions on how to use this prompt effectively. For example:

1. Replace [TOPIC] with your specific subject
2. Adjust the tone based on your audience
3. Add any specific requirements or constraints

This helps other users get the best results from your prompt."
              />
              <p className="text-sm text-gray-500 mt-1">
                Help others understand how to use your prompt effectively. Include tips for customizing placeholders and getting the best results.
              </p>
            </div>

            {/* Example */}
            <div>
              <label htmlFor="example" className="block text-sm font-medium text-gray-700 mb-2">
                Example Usage (Optional)
              </label>
              <textarea
                id="example"
                name="example"
                value={formData.example}
                onChange={handleInputChange}
                rows={8}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Show an example of your prompt in action. For instance:

Original prompt: 'Write a [TYPE] about [TOPIC] for [AUDIENCE]'

Example usage:
'Write a blog post about sustainable living for young professionals'

Expected output: A well-structured blog post that discusses sustainable living practices specifically tailored for busy young professionals, including practical tips they can implement in their daily lives."
              />
              <p className="text-sm text-gray-500 mt-1">
                Provide a concrete example showing how to use your prompt and what kind of output to expect. This helps users understand the prompt's potential.
              </p>
            </div>

            {/* Tags */}
            <div>
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <input
                type="text"
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter tags separated by commas (e.g., writing, email, professional)"
              />
              <p className="text-sm text-gray-500 mt-1">
                Separate multiple tags with commas
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={() => router.push(`/prompts/${promptId}`)}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Updating...' : 'Update Prompt'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
