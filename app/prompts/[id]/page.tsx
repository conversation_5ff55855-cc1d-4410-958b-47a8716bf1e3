'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Heart } from 'lucide-react';

interface Prompt {
  _id: string;
  title: string;
  description: string;
  prompt_text: string;
  instructions?: string;
  example?: string;
  platform: string;
  category: {
    _id: string;
    name: string;
  };
  tags: string[];
  ratings: Array<{ user: string; value: number }>;
  verified: boolean;
  created_by: {
    _id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function PromptDetailPage() {
  const { data: session } = useSession();
  const params = useParams();
  const router = useRouter();
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRating, setIsRating] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [isFavoriting, setIsFavoriting] = useState(false);

  useEffect(() => {
    if (params?.id) {
      loadPrompt();
    }
  }, [params?.id]);

  const loadPrompt = async () => {
    if (!params?.id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/prompts/${params.id}`);
      const data = await response.json();

      if (response.ok) {
        setPrompt(data);
        // Log view for analytics
        if (session?.user?.id && params?.id) {
          fetch(`/api/prompts/${params.id}/view`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
          }).catch(err => console.error('Error logging view:', err));
        }
        // Check favorite status
        checkFavoriteStatus();
      } else {
        setError(data.message || 'Prompt not found');
      }
    } catch (err) {
      setError('Failed to load prompt');
      console.error('Error loading prompt:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRate = async (value: number) => {
    if (!session?.user?.id || !prompt || isRating) return;

    setIsRating(true);
    try {
      const response = await fetch(`/api/prompts/${prompt._id}/rate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ value }),
      });

      if (response.ok) {
        // Update local state
        const existingRatingIndex = prompt.ratings.findIndex(
          r => r.user === session.user.id
        );

        const newRatings = [...prompt.ratings];
        if (existingRatingIndex !== -1) {
          newRatings[existingRatingIndex] = { user: session.user.id, value };
        } else {
          newRatings.push({ user: session.user.id, value });
        }

        setPrompt({ ...prompt, ratings: newRatings });
      }
    } catch (error) {
      console.error('Error rating prompt:', error);
    } finally {
      setIsRating(false);
    }
  };

  const copyToClipboard = async () => {
    if (!prompt) return;

    try {
      await navigator.clipboard.writeText(prompt.prompt_text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const checkFavoriteStatus = async () => {
    if (!session?.user?.id || !params?.id) return;

    try {
      const response = await fetch(`/api/prompts/${params.id}/favorite`);
      if (response.ok) {
        const data = await response.json();
        setIsFavorited(data.isFavorited);
      }
    } catch (error) {
      console.error('Error checking favorite status:', error);
    }
  };

  const handleFavorite = async () => {
    if (!session?.user?.id || !params?.id || isFavoriting) return;

    setIsFavoriting(true);
    try {
      const method = isFavorited ? 'DELETE' : 'POST';
      const response = await fetch(`/api/prompts/${params.id}/favorite`, {
        method,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        setIsFavorited(data.isFavorited);
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    } finally {
      setIsFavoriting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading prompt...</span>
      </div>
    );
  }

  if (error || !prompt) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error || 'Prompt not found'}</div>
          <Link
            href="/browse"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Browse
          </Link>
        </div>
      </div>
    );
  }

  // Calculate rating statistics
  const upvotes = prompt.ratings.filter(r => r.value === 1).length;
  const downvotes = prompt.ratings.filter(r => r.value === -1).length;
  const totalRating = upvotes - downvotes;
  const userRating = session?.user?.id
    ? prompt.ratings.find(r => r.user === session.user.id)?.value
    : null;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link
              href="/browse"
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              ← Back to Browse
            </Link>
            <div className="flex items-center gap-4">
              {session?.user?.id && (
                <>
                  {/* Favorite Button */}
                  <button
                    onClick={handleFavorite}
                    disabled={isFavoriting}
                    className={`p-2 rounded-lg transition-all duration-200 ${
                      isFavorited
                        ? 'text-red-600 bg-red-50 hover:bg-red-100'
                        : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
                    } ${isFavoriting ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}`}
                    title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
                  >
                    <Heart className={`w-5 h-5 ${isFavorited ? 'fill-current' : ''}`} />
                  </button>

                  {/* Rating Buttons */}
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleRate(1)}
                      disabled={isRating}
                      className={`p-2 rounded transition-colors ${
                        userRating === 1
                          ? 'text-green-600 bg-green-50'
                          : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                      } disabled:opacity-50`}
                    >
                      ▲
                    </button>
                    <span className={`text-lg font-medium ${
                      totalRating > 0 ? 'text-green-600' :
                      totalRating < 0 ? 'text-red-600' : 'text-gray-500'
                    }`}>
                      {totalRating}
                    </span>
                    <button
                      onClick={() => handleRate(-1)}
                      disabled={isRating}
                      className={`p-2 rounded transition-colors ${
                        userRating === -1
                          ? 'text-red-600 bg-red-50'
                          : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                      } disabled:opacity-50`}
                    >
                      ▼
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm border p-8">
          {/* Title and Meta */}
          <div className="mb-6">
            <div className="flex items-start justify-between mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{prompt.title}</h1>
              {prompt.verified && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  ✓ Verified
                </span>
              )}
            </div>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-medium">
                {prompt.platform}
              </span>
              <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-800">
                {prompt.category.name}
              </span>
              <span>By {prompt.created_by.name}</span>
              <span>Created {formatDate(prompt.createdAt)}</span>
              {prompt.updatedAt !== prompt.createdAt && (
                <span>Updated {formatDate(prompt.updatedAt)}</span>
              )}
            </div>

            <p className="text-gray-700 text-lg leading-relaxed">
              {prompt.description}
            </p>
          </div>

          {/* Prompt Text */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-xl font-semibold text-gray-900">Prompt</h2>
              <button
                onClick={copyToClipboard}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  copied
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {copied ? '✓ Copied!' : 'Copy'}
              </button>
            </div>
            <div className="bg-gray-50 rounded-lg p-6 border">
              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                {prompt.prompt_text}
              </pre>
            </div>
          </div>

          {/* Instructions and Example from Creator */}
          {(prompt.instructions || prompt.example) && (
            <div className="mb-6">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  How to Use This Prompt
                </h3>

                {prompt.instructions && (
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <span className="text-blue-500 mr-2">📋</span>
                      Instructions
                    </h4>
                    <div className="bg-white rounded-lg p-4 border border-gray-200">
                      <pre className="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed">
                        {prompt.instructions}
                      </pre>
                    </div>
                  </div>
                )}

                {prompt.example && (
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <span className="text-green-500 mr-2">💡</span>
                      Example Usage
                    </h4>
                    <div className="bg-white rounded-lg p-4 border border-gray-200">
                      <pre className="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed">
                        {prompt.example}
                      </pre>
                    </div>
                  </div>
                )}

                {/* Quick Tips */}
                <div className="p-4 bg-white rounded-lg border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <span className="text-purple-500 mr-2">🎯</span>
                    Quick Tips
                  </h4>
                  <div className="text-sm text-gray-700">
                    <ul className="space-y-1">
                      <li>• Copy the prompt using the button above</li>
                      <li>• Replace any [PLACEHOLDERS] with your specific information</li>
                      <li>• Paste into {prompt.platform} and customize as needed</li>
                      <li>• Rate this prompt to help the community!</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tags */}
          {prompt.tags.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {prompt.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Rating Stats */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Community Rating</h3>
            <div className="flex items-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <span className="text-green-600">▲</span>
                <span>{upvotes} upvotes</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-red-600">▼</span>
                <span>{downvotes} downvotes</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">Total:</span>
                <span className={`font-medium ${
                  totalRating > 0 ? 'text-green-600' :
                  totalRating < 0 ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {totalRating > 0 ? '+' : ''}{totalRating}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}