'use client';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import {
  User,
  Mail,
  Shield,
  Calendar,
  Activity,
  FileText,
  Star,
  TrendingUp,
  Edit3,
  Settings,
  Award,
  Clock,
  Eye,
  ThumbsUp,
  <PERSON>rk<PERSON>,
  Heart
} from 'lucide-react';

export default function ProfilePage() {
  const { data: session } = useSession();
  const [user, setUser] = useState<any>(null);
  const [userStats, setUserStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch user profile
        const userRes = await fetch('/api/users/me');
        const userData = await userRes.json();
        setUser(userData);

        // Fetch user statistics (you might need to create this endpoint)
        try {
          const statsRes = await fetch('/api/user/stats');
          if (statsRes.ok) {
            const statsData = await statsRes.json();
            setUserStats(statsData);
          }
        } catch (statsError) {
          // Stats are optional, continue without them
          console.log('Stats not available:', statsError);
        }

        setLoading(false);
      } catch (err) {
        setError('Failed to load user profile');
        setLoading(false);
      }
    };

    if (session) {
      fetchUserData();
    }
  }, [session]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-destructive text-destructive-foreground';
      case 'moderator': return 'bg-warning text-warning-foreground';
      case 'user': return 'bg-primary text-primary-foreground';
      default: return 'bg-secondary text-secondary-foreground';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return Shield;
      case 'moderator': return Award;
      default: return User;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
          <span className="text-muted-foreground">Loading profile...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-destructive text-lg mb-4">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <User className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <div className="text-lg text-foreground mb-2">User not found</div>
          <Link href="/" className="btn btn-primary">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  const RoleIcon = getRoleIcon(user.role);

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-primary/10 via-accent/10 to-primary/10 border-b border-border">
        <div className="container-custom py-12">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
            {/* Profile Info */}
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
              {/* Avatar */}
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-primary rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-3xl font-bold text-white">
                    {user.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-success rounded-full border-4 border-background flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>

              {/* User Details */}
              <div className="text-center sm:text-left">
                <h1 className="text-3xl font-bold text-foreground mb-2">{user.name}</h1>
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-4">
                  <div className="flex items-center justify-center sm:justify-start space-x-2 text-muted-foreground">
                    <Mail className="w-4 h-4" />
                    <span>{user.email}</span>
                  </div>
                  <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(user.role)}`}>
                    <RoleIcon className="w-4 h-4" />
                    <span className="capitalize">{user.role}</span>
                  </div>
                </div>
                <div className="flex items-center justify-center sm:justify-start space-x-2 text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4" />
                  <span>Member since {new Date(user.createdAt || Date.now()).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                  })}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Link href="/settings" className="btn btn-outline btn-lg">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Link>
              <Link href="/favorites" className="btn btn-outline btn-lg">
                <Heart className="w-4 h-4 mr-2" />
                Favorites
              </Link>
              <Link href="/my-prompts" className="btn btn-primary btn-lg">
                <FileText className="w-4 h-4 mr-2" />
                My Prompts
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Statistics Cards */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats Overview */}
            <div>
              <h2 className="text-2xl font-semibold text-foreground mb-6 flex items-center">
                <TrendingUp className="w-6 h-6 mr-3 text-primary" />
                Statistics Overview
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                {[
                  {
                    icon: FileText,
                    label: 'Total Prompts',
                    value: userStats?.totalPrompts?.toLocaleString() || '0',
                    color: 'text-blue-600',
                    bgColor: 'bg-blue-50',
                    darkBgColor: 'dark:bg-blue-950',
                    description: userStats?.verifiedPrompts ? `${userStats.verifiedPrompts} verified` : 'No verified prompts yet'
                  },
                  {
                    icon: Eye,
                    label: 'Total Views',
                    value: userStats?.totalViews?.toLocaleString() || '0',
                    color: 'text-green-600',
                    bgColor: 'bg-green-50',
                    darkBgColor: 'dark:bg-green-950',
                    description: userStats?.thisMonthPrompts ? `${userStats.thisMonthPrompts} this month` : 'No views yet'
                  },
                  {
                    icon: ThumbsUp,
                    label: 'Total Likes',
                    value: userStats?.totalLikes?.toLocaleString() || '0',
                    color: 'text-purple-600',
                    bgColor: 'bg-purple-50',
                    darkBgColor: 'dark:bg-purple-950',
                    description: userStats?.totalRatings ? `${userStats.totalRatings} total ratings` : 'No ratings yet'
                  },
                  {
                    icon: Star,
                    label: 'Avg Rating',
                    value: userStats?.averageRating && userStats.averageRating > 0 ? `${userStats.averageRating}★` : 'N/A',
                    color: 'text-yellow-600',
                    bgColor: 'bg-yellow-50',
                    darkBgColor: 'dark:bg-yellow-950',
                    description: userStats?.highRatedPrompts ? `${userStats.highRatedPrompts} highly rated` : 'No ratings yet'
                  },
                ].map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <div key={stat.label} className="card p-6 hover-lift">
                      <div className={`w-12 h-12 ${stat.bgColor} ${stat.darkBgColor} rounded-lg flex items-center justify-center mb-4`}>
                        <Icon className={`w-6 h-6 ${stat.color}`} />
                      </div>
                      <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                      <div className="text-sm text-muted-foreground mb-1">{stat.label}</div>
                      <div className="text-xs text-muted-foreground">{stat.description}</div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h2 className="text-2xl font-semibold text-foreground mb-6 flex items-center">
                <Activity className="w-6 h-6 mr-3 text-primary" />
                Recent Activity
              </h2>
              <div className="card p-6">
                {user.activity_log?.length ? (
                  <div className="space-y-4">
                    {user.activity_log.slice(0, 10).map((log: any, i: number) => (
                      <div key={i} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-surface-hover transition-colors">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm text-foreground font-medium">{log.action}</div>
                          <div className="text-xs text-muted-foreground flex items-center mt-1">
                            <Clock className="w-3 h-3 mr-1" />
                            {new Date(log.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                    {user.activity_log.length > 10 && (
                      <div className="text-center pt-4 border-t border-border">
                        <button className="btn btn-ghost btn-sm">
                          View All Activity
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-16">
                    <Activity className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
                    <div className="text-xl font-semibold text-foreground mb-3">No activity yet</div>
                    <div className="text-muted-foreground mb-8 max-w-md mx-auto leading-relaxed">
                      Start creating prompts to see your activity here. Share your creativity with the community!
                    </div>
                    <Link href="/create" className="btn btn-primary btn-xl hover-lift hover-glow">
                      <Sparkles className="w-5 h-5 mr-3" />
                      Create Your First Prompt
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Quick Actions</h3>
              <div className="space-y-4">
                <Link href="/create" className="btn btn-primary btn-lg w-full justify-start h-14 text-base">
                  <Edit3 className="w-5 h-5 mr-4" />
                  Create New Prompt
                </Link>
                <Link href="/favorites" className="btn btn-outline btn-lg w-full justify-start h-14 text-base">
                  <Heart className="w-5 h-5 mr-4" />
                  View Favorites
                </Link>
                <Link href="/my-prompts" className="btn btn-outline btn-lg w-full justify-start h-14 text-base">
                  <FileText className="w-5 h-5 mr-4" />
                  View My Prompts
                </Link>
                <Link href="/browse" className="btn btn-outline btn-lg w-full justify-start h-14 text-base">
                  <Eye className="w-5 h-5 mr-4" />
                  Browse All Prompts
                </Link>
                <Link href="/settings" className="btn btn-outline btn-lg w-full justify-start h-14 text-base">
                  <Settings className="w-5 h-5 mr-4" />
                  Account Settings
                </Link>
              </div>
            </div>

            {/* Account Info */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">Account Information</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Account Status</span>
                  <span className="badge badge-accent">Active</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Email Verified</span>
                  <span className="badge badge-success">Verified</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Two-Factor Auth</span>
                  <span className="badge badge-secondary">Disabled</span>
                </div>
                <div className="pt-4 border-t border-border">
                  <Link href="/settings" className="text-sm text-primary hover:text-primary-hover">
                    Manage Account Settings →
                  </Link>
                </div>
              </div>
            </div>

            {/* Achievements */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                <Award className="w-5 h-5 mr-2 text-primary" />
                Achievements
              </h3>
              <div className="space-y-3">
                {userStats?.totalPrompts >= 1 ? (
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-success-light rounded-full flex items-center justify-center">
                      <Sparkles className="w-4 h-4 text-success" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">First Prompt</div>
                      <div className="text-xs text-muted-foreground">Created your first prompt</div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3 opacity-50">
                    <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                      <Sparkles className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">First Prompt</div>
                      <div className="text-xs text-muted-foreground">Create your first prompt to unlock</div>
                    </div>
                  </div>
                )}

                {userStats?.totalPrompts >= 5 ? (
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary-light rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-primary" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">Contributor</div>
                      <div className="text-xs text-muted-foreground">Created 5+ prompts</div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3 opacity-50">
                    <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Contributor</div>
                      <div className="text-xs text-muted-foreground">{userStats?.totalPrompts || 0}/5 prompts created</div>
                    </div>
                  </div>
                )}

                {userStats?.totalViews >= 100 ? (
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-accent-light rounded-full flex items-center justify-center">
                      <Eye className="w-4 h-4 text-accent" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">Popular Creator</div>
                      <div className="text-xs text-muted-foreground">100+ total views</div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3 opacity-50">
                    <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                      <Eye className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Popular Creator</div>
                      <div className="text-xs text-muted-foreground">{userStats?.totalViews || 0}/100 views</div>
                    </div>
                  </div>
                )}

                {userStats?.averageRating >= 4 ? (
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Star className="w-4 h-4 text-yellow-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">Highly Rated</div>
                      <div className="text-xs text-muted-foreground">Average rating 4+ stars</div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3 opacity-50">
                    <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                      <Star className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Highly Rated</div>
                      <div className="text-xs text-muted-foreground">Get 4+ star average rating</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}