# API Troubleshooting Guide

## Issue: Intermittent "Internal server error" on `/api/prompts/{id}`

### Symptoms
- API endpoint `https://prompt.omtel.dev/api/prompts/6888f687d051ccad65e2c556` sometimes returns:
  ```json
  {"message":"Internal server error"}
  ```
- Issue is intermittent - sometimes works, sometimes fails
- Local development works fine

### Root Causes Analysis

#### 1. Database Connection Issues (Most Likely)
- **Cause**: MongoDB Atlas connection timeouts or network issues
- **Symptoms**: Intermittent failures, works locally
- **Solution**: Enhanced connection configuration with retries

#### 2. Cold Start Issues (Serverless)
- **Cause**: If deployed on Vercel/Netlify, cold starts can cause timeouts
- **Symptoms**: First request after inactivity fails
- **Solution**: Keep-alive requests or dedicated server

#### 3. Memory/Resource Limits
- **Cause**: Server running out of memory or hitting resource limits
- **Symptoms**: Failures under load
- **Solution**: Monitor resource usage, optimize queries

### Implemented Solutions

#### ✅ Enhanced Database Connection
- Added connection pooling and timeout configurations
- Implemented retry logic with exponential backoff
- Better error handling and logging

#### ✅ Improved Error Handling
- Added specific error types and messages
- Enhanced logging with request context
- Input validation for ObjectId format

#### ✅ Health Check Endpoint
- New endpoint: `/api/health`
- Monitors database connectivity
- Provides system status information

#### ✅ Monitoring Tools
- API monitoring script: `scripts/monitor-api.js`
- Enhanced logging with request tracking
- Database operation retry mechanism

### Monitoring Commands

#### 1. Check API Health
```bash
curl https://prompt.omtel.dev/api/health
```

#### 2. Monitor API Continuously
```bash
node scripts/monitor-api.js
```

#### 3. Test Specific Endpoint
```bash
curl -v "https://prompt.omtel.dev/api/prompts/6888f687d051ccad65e2c556"
```

### Production Deployment Checklist

#### Environment Variables
- [ ] `MONGODB_URI` is correctly set
- [ ] `NEXTAUTH_SECRET` is configured
- [ ] `NODE_ENV=production`

#### Database Configuration
- [ ] MongoDB Atlas cluster is running
- [ ] Network access is configured for your deployment platform
- [ ] Connection string includes retry options

#### Server Configuration
- [ ] Adequate memory allocation
- [ ] Proper timeout configurations
- [ ] Error logging enabled

### Next Steps for Production

1. **Deploy the fixes** to your production environment
2. **Monitor the health endpoint** regularly
3. **Run the monitoring script** to track success rates
4. **Check server logs** for detailed error information
5. **Consider upgrading** MongoDB Atlas tier if connection issues persist

### Emergency Actions

If the issue persists after deployment:

1. **Check MongoDB Atlas Status**
   - Login to MongoDB Atlas dashboard
   - Check cluster health and metrics
   - Review connection logs

2. **Verify Network Configuration**
   - Ensure deployment platform IP is whitelisted
   - Check firewall rules
   - Test connection from deployment environment

3. **Scale Resources**
   - Increase server memory/CPU if possible
   - Consider upgrading MongoDB Atlas tier
   - Implement caching layer

4. **Fallback Options**
   - Implement graceful degradation
   - Add circuit breaker pattern
   - Consider read replicas for high availability

### Contact Information
- MongoDB Atlas Support: [Atlas Support](https://support.mongodb.com/)
- Deployment Platform Support: Check your hosting provider's documentation
