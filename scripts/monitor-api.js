#!/usr/bin/env node

/**
 * API Monitoring Script
 * Monitors the production API endpoint for intermittent failures
 */

const https = require('https');
const fs = require('fs');

const CONFIG = {
  baseUrl: 'https://prompt.omtel.dev',
  endpoints: [
    '/api/health',
    '/api/prompts/6888f687d051ccad65e2c556'
  ],
  interval: 30000, // 30 seconds
  timeout: 10000, // 10 seconds
  logFile: 'api-monitor.log'
};

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  console.log(logMessage.trim());
  fs.appendFileSync(CONFIG.logFile, logMessage);
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = https.get(url, { timeout: CONFIG.timeout }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const duration = Date.now() - startTime;
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          duration: duration
        });
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkEndpoint(endpoint) {
  const url = `${CONFIG.baseUrl}${endpoint}`;
  
  try {
    const response = await makeRequest(url);
    
    if (response.statusCode === 200) {
      log(`✅ ${endpoint} - OK (${response.duration}ms)`);
      return { success: true, duration: response.duration };
    } else {
      log(`❌ ${endpoint} - HTTP ${response.statusCode} (${response.duration}ms)`);
      log(`   Response: ${response.data.substring(0, 200)}...`);
      return { success: false, statusCode: response.statusCode, duration: response.duration };
    }
  } catch (error) {
    log(`💥 ${endpoint} - ERROR: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runMonitoring() {
  log('🚀 Starting API monitoring...');
  
  let successCount = 0;
  let errorCount = 0;
  let totalRequests = 0;
  
  const monitor = async () => {
    log('--- Monitoring cycle ---');
    
    for (const endpoint of CONFIG.endpoints) {
      const result = await checkEndpoint(endpoint);
      totalRequests++;
      
      if (result.success) {
        successCount++;
      } else {
        errorCount++;
      }
    }
    
    const successRate = ((successCount / totalRequests) * 100).toFixed(2);
    log(`📊 Stats: ${successCount}/${totalRequests} successful (${successRate}%)`);
    
    setTimeout(monitor, CONFIG.interval);
  };
  
  monitor();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('🛑 Monitoring stopped by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('🛑 Monitoring terminated');
  process.exit(0);
});

// Start monitoring
runMonitoring().catch(error => {
  log(`💥 Monitoring failed: ${error.message}`);
  process.exit(1);
});
