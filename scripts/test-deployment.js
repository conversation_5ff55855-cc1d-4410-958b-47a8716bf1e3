#!/usr/bin/env node

/**
 * Test Deployment Script
 * Tests the production deployment after changes are pushed
 */

const https = require('https');

const CONFIG = {
  baseUrl: 'https://prompt.omtel.dev',
  maxWaitTime: 300000, // 5 minutes
  checkInterval: 30000, // 30 seconds
};

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = https.get(url, { timeout: 10000 }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const duration = Date.now() - startTime;
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          duration: duration
        });
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.on('error', (error) => {
      reject(error);
    });
  });
}

async function testHealthEndpoint() {
  const url = `${CONFIG.baseUrl}/api/health`;
  
  try {
    const response = await makeRequest(url);
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.data);
      console.log('✅ Health endpoint is working!');
      console.log(`   Status: ${data.status}`);
      console.log(`   Database connected: ${data.database.connected}`);
      console.log(`   Response time: ${response.duration}ms`);
      return true;
    } else {
      console.log(`❌ Health endpoint returned HTTP ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Health endpoint error: ${error.message}`);
    return false;
  }
}

async function testPromptEndpoint() {
  const url = `${CONFIG.baseUrl}/api/prompts/6888f687d051ccad65e2c556`;
  
  try {
    const response = await makeRequest(url);
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.data);
      console.log('✅ Prompt endpoint is working!');
      console.log(`   Prompt title: ${data.title}`);
      console.log(`   Response time: ${response.duration}ms`);
      return true;
    } else {
      console.log(`❌ Prompt endpoint returned HTTP ${response.statusCode}`);
      console.log(`   Response: ${response.data.substring(0, 100)}...`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Prompt endpoint error: ${error.message}`);
    return false;
  }
}

async function waitForDeployment() {
  console.log('🚀 Waiting for deployment to complete...');
  console.log('   This may take a few minutes...\n');
  
  const startTime = Date.now();
  let attempt = 1;
  
  while (Date.now() - startTime < CONFIG.maxWaitTime) {
    console.log(`📡 Testing deployment (attempt ${attempt})...`);
    
    // Test health endpoint first (new endpoint)
    const healthWorking = await testHealthEndpoint();
    
    if (healthWorking) {
      console.log('\n🎉 Deployment successful! Testing the original endpoint...\n');
      
      // Test the original problematic endpoint
      const promptWorking = await testPromptEndpoint();
      
      if (promptWorking) {
        console.log('\n🎊 All endpoints are working correctly!');
        console.log('   The intermittent error issue should now be resolved.');
        return true;
      }
    }
    
    console.log(`   Waiting ${CONFIG.checkInterval / 1000} seconds before next check...\n`);
    await new Promise(resolve => setTimeout(resolve, CONFIG.checkInterval));
    attempt++;
  }
  
  console.log('⏰ Timeout waiting for deployment. Please check manually.');
  return false;
}

// Start testing
waitForDeployment().then(success => {
  if (success) {
    console.log('\n✨ You can now run the monitoring script to track ongoing performance:');
    console.log('   node scripts/monitor-api.js');
  } else {
    console.log('\n🔍 Manual verification needed:');
    console.log('   curl https://prompt.omtel.dev/api/health');
    console.log('   curl https://prompt.omtel.dev/api/prompts/6888f687d051ccad65e2c556');
  }
}).catch(error => {
  console.error('💥 Testing failed:', error.message);
  process.exit(1);
});
