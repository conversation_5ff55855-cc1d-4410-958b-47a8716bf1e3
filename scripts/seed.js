const mongoose = require('mongoose');
require('dotenv').config();

// Define schemas directly since we can't easily import TS models in Node.js
const bcrypt = require('bcryptjs');

// User Schema
const UserSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['user', 'admin', 'moderator', 'super admin'], default: 'user' },
  activity_log: [
    {
      action: String,
      timestamp: Date,
    },
  ],
}, { timestamps: true });

UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Category Schema
const CategorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  platform: { type: String, required: true },
  description: { type: String },
});

// Prompt Schema
const PromptSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  prompt_text: { type: String, required: true },
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
  platform: { type: String, required: true },
  tags: [{ type: String }],
  ratings: [
    {
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      value: { type: Number, enum: [1, -1] },
    },
  ],
  created_by: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  verified: { type: Boolean, default: false },
}, { timestamps: true });

// Analytics Schema
const AnalyticsSchema = new mongoose.Schema({
  prompt_views: [
    {
      prompt: { type: mongoose.Schema.Types.ObjectId, ref: 'Prompt' },
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      timestamp: { type: Date, default: Date.now },
    },
  ],
  search_queries: [
    {
      query: String,
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      timestamp: { type: Date, default: Date.now },
    },
  ],
  rating_activities: [
    {
      prompt: { type: mongoose.Schema.Types.ObjectId, ref: 'Prompt' },
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      value: Number,
      timestamp: { type: Date, default: Date.now },
    },
  ],
}, { timestamps: true });

async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get or create models
    const User = mongoose.models.User || mongoose.model('User', UserSchema);
    const Category = mongoose.models.Category || mongoose.model('Category', CategorySchema);
    const Prompt = mongoose.models.Prompt || mongoose.model('Prompt', PromptSchema);
    const Analytics = mongoose.models.Analytics || mongoose.model('Analytics', AnalyticsSchema);

    // Clear existing data (except admin user)
    await Category.deleteMany({});
    await Prompt.deleteMany({});
    await Analytics.deleteMany({});
    console.log('Cleared existing data');

    // Create categories
    const categories = [
      { name: 'Summarization', platform: 'ChatGPT', description: 'Prompts for summarizing content' },
      { name: 'Code Review', platform: 'ChatGPT', description: 'Code review and improvement prompts' },
      { name: 'Writing', platform: 'ChatGPT', description: 'Creative and technical writing prompts' },
      { name: 'Analysis', platform: 'ChatGPT', description: 'Data and text analysis prompts' },
      { name: 'Translation', platform: 'ChatGPT', description: 'Language translation prompts' },
      { name: 'Education', platform: 'ChatGPT', description: 'Educational and learning prompts' },
      { name: 'Code Generation', platform: 'GitHub Copilot', description: 'Code generation prompts for Copilot' },
      { name: 'Documentation', platform: 'GitHub Copilot', description: 'Code documentation prompts' },
      { name: 'Testing', platform: 'GitHub Copilot', description: 'Unit testing and QA prompts' },
      { name: 'Image Generation', platform: 'Midjourney', description: 'Creative image generation prompts' },
      { name: 'Character Design', platform: 'Midjourney', description: 'Character and portrait prompts' },
      { name: 'Architecture', platform: 'Midjourney', description: 'Architectural visualization prompts' },
    ];

    const createdCategories = await Category.insertMany(categories);
    console.log('Created categories:', createdCategories.length);

    // Get admin user (should already exist)
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = await User.create({
        email: '<EMAIL>',
        name: 'Admin User',
        password: 'snsn1133',
        role: 'admin',
        activity_log: []
      });
    }

    // Create additional sample users
    const sampleUsers = await User.insertMany([
      {
        email: '<EMAIL>',
        name: 'John Doe',
        password: 'password123',
        role: 'user',
        activity_log: []
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        password: 'password123',
        role: 'moderator',
        activity_log: []
      },
      {
        email: '<EMAIL>',
        name: 'Mike Wilson',
        password: 'password123',
        role: 'user',
        activity_log: []
      }
    ]);

    console.log('Created sample users:', sampleUsers.length);

    // Create comprehensive sample prompts
    const allUsers = [adminUser, ...sampleUsers];
    const prompts = [
      // ChatGPT Summarization Prompts
      {
        title: 'Article Summarizer',
        description: 'Summarize long articles into key points with actionable insights',
        prompt_text: 'Please summarize the following article into 3-5 key points, highlighting the most important insights and actionable takeaways:\n\n[ARTICLE_TEXT]\n\nFormat your response as:\n• Key Point 1: [insight]\n• Key Point 2: [insight]\n• Key Point 3: [insight]\n• Action Items: [what readers should do]',
        category: createdCategories.find(c => c.name === 'Summarization')._id,
        platform: 'ChatGPT',
        tags: ['summary', 'article', 'key-points', 'insights'],
        created_by: allUsers[0]._id,
        verified: true,
        ratings: [
          { user: allUsers[1]._id, value: 1 },
          { user: allUsers[2]._id, value: 1 },
          { user: allUsers[3]._id, value: 1 }
        ]
      },
      {
        title: 'Meeting Notes Summarizer',
        description: 'Convert meeting transcripts into structured summaries',
        prompt_text: 'Transform this meeting transcript into a structured summary:\n\n[MEETING_TRANSCRIPT]\n\nPlease organize as:\n## Key Decisions\n## Action Items (with owners and deadlines)\n## Next Steps\n## Open Questions',
        category: createdCategories.find(c => c.name === 'Summarization')._id,
        platform: 'ChatGPT',
        tags: ['meeting', 'summary', 'action-items', 'business'],
        created_by: allUsers[1]._id,
        verified: true,
        ratings: [
          { user: allUsers[0]._id, value: 1 },
          { user: allUsers[2]._id, value: 1 }
        ]
      },

      // ChatGPT Code Review Prompts
      {
        title: 'Comprehensive Code Review',
        description: 'Detailed code review focusing on best practices, security, and performance',
        prompt_text: 'Please review the following code and provide detailed feedback on:\n\n1. **Code Quality & Best Practices**\n2. **Performance Optimizations**\n3. **Security Considerations**\n4. **Readability & Maintainability**\n5. **Potential Bugs or Edge Cases**\n\n```[LANGUAGE]\n[CODE_HERE]\n```\n\nFor each issue found, please provide:\n- Severity level (Critical/High/Medium/Low)\n- Explanation of the issue\n- Suggested improvement with code example',
        category: createdCategories.find(c => c.name === 'Code Review')._id,
        platform: 'ChatGPT',
        tags: ['code-review', 'best-practices', 'security', 'performance'],
        created_by: allUsers[2]._id,
        verified: true,
        ratings: [
          { user: allUsers[0]._id, value: 1 },
          { user: allUsers[1]._id, value: 1 },
          { user: allUsers[3]._id, value: 1 }
        ]
      },
      {
        title: 'API Design Review',
        description: 'Review API design for RESTful principles and best practices',
        prompt_text: 'Review this API design for RESTful principles and best practices:\n\n[API_SPECIFICATION]\n\nEvaluate:\n- Resource naming conventions\n- HTTP method usage\n- Status code appropriateness\n- Request/response structure\n- Authentication/authorization\n- Error handling\n- Documentation clarity\n\nProvide specific recommendations for improvement.',
        category: createdCategories.find(c => c.name === 'Code Review')._id,
        platform: 'ChatGPT',
        tags: ['api', 'rest', 'design', 'architecture'],
        created_by: allUsers[3]._id,
        verified: true,
        ratings: [
          { user: allUsers[1]._id, value: 1 },
          { user: allUsers[2]._id, value: 1 }
        ]
      },

      // ChatGPT Writing Prompts
      {
        title: 'Technical Blog Post Generator',
        description: 'Create comprehensive technical blog posts with examples and best practices',
        prompt_text: 'Write a comprehensive technical blog post about [TOPIC]. Structure it as follows:\n\n## Introduction\n- Hook the reader with a compelling opening\n- Explain why this topic matters\n- Preview what they\'ll learn\n\n## Main Content (3-5 sections)\n- Include practical examples\n- Add code snippets where relevant\n- Explain complex concepts simply\n\n## Best Practices\n- List 5-7 actionable best practices\n- Include do\'s and don\'ts\n\n## Conclusion\n- Summarize key takeaways\n- Provide next steps for readers\n\nTarget audience: [AUDIENCE_LEVEL]\nTone: Professional but approachable\nLength: 1500-2000 words',
        category: createdCategories.find(c => c.name === 'Writing')._id,
        platform: 'ChatGPT',
        tags: ['blog', 'technical-writing', 'content-creation', 'tutorial'],
        created_by: allUsers[0]._id,
        verified: true,
        ratings: [
          { user: allUsers[1]._id, value: 1 },
          { user: allUsers[2]._id, value: 1 },
          { user: allUsers[3]._id, value: 1 }
        ]
      },

      // GitHub Copilot Prompts
      {
        title: 'React Component with TypeScript',
        description: 'Generate React functional components with TypeScript and best practices',
        prompt_text: '// Create a React functional component with the following specifications:\n// - Component name: [COMPONENT_NAME]\n// - TypeScript with proper interfaces\n// - Props validation\n// - useState and useEffect hooks as needed\n// - Responsive design with Tailwind CSS\n// - Accessibility features (ARIA labels, keyboard navigation)\n// - Error boundary handling\n// - Loading states\n// - JSDoc comments for documentation\n\n// Functionality: [DESCRIBE_FUNCTIONALITY]\n// Props: [LIST_PROPS_AND_TYPES]\n\ninterface [COMPONENT_NAME]Props {\n  // Define props here\n}\n\nconst [COMPONENT_NAME]: React.FC<[COMPONENT_NAME]Props> = ({}) => {\n  // Implementation here\n};',
        category: createdCategories.find(c => c.name === 'Code Generation')._id,
        platform: 'GitHub Copilot',
        tags: ['react', 'typescript', 'component', 'hooks'],
        created_by: allUsers[1]._id,
        verified: true,
        ratings: [
          { user: allUsers[0]._id, value: 1 },
          { user: allUsers[2]._id, value: 1 }
        ]
      },

      // Midjourney Prompts
      {
        title: 'Professional Portrait Photography',
        description: 'Generate high-quality professional portraits with studio lighting',
        prompt_text: 'Professional headshot portrait of [SUBJECT_DESCRIPTION], studio lighting setup with key light and fill light, shallow depth of field, shot with 85mm lens, clean background, confident expression, business attire, high resolution, photorealistic, commercial photography style --ar 3:4 --style raw --v 6',
        category: createdCategories.find(c => c.name === 'Character Design')._id,
        platform: 'Midjourney',
        tags: ['portrait', 'professional', 'photography', 'business'],
        created_by: allUsers[2]._id,
        verified: true,
        ratings: [
          { user: allUsers[0]._id, value: 1 },
          { user: allUsers[1]._id, value: 1 },
          { user: allUsers[3]._id, value: 1 }
        ]
      },
      {
        title: 'Modern Architecture Visualization',
        description: 'Create stunning architectural renderings of modern buildings',
        prompt_text: 'Modern architectural visualization of [BUILDING_TYPE], minimalist design, clean lines, glass and steel construction, natural lighting, surrounded by landscaping, blue hour lighting, photorealistic rendering, architectural photography style, shot with wide-angle lens, high detail, professional architectural visualization --ar 16:9 --v 6',
        category: createdCategories.find(c => c.name === 'Architecture')._id,
        platform: 'Midjourney',
        tags: ['architecture', 'modern', 'visualization', 'building'],
        created_by: allUsers[3]._id,
        verified: true,
        ratings: [
          { user: allUsers[0]._id, value: 1 },
          { user: allUsers[1]._id, value: 1 }
        ]
      }
    ];

    const createdPrompts = await Prompt.insertMany(prompts);
    console.log('Created prompts:', createdPrompts.length);

    // Create sample analytics data
    const analyticsData = {
      prompt_views: [
        { prompt: createdPrompts[0]._id, user: allUsers[1]._id, timestamp: new Date(Date.now() - 86400000) },
        { prompt: createdPrompts[0]._id, user: allUsers[2]._id, timestamp: new Date(Date.now() - 82800000) },
        { prompt: createdPrompts[1]._id, user: allUsers[0]._id, timestamp: new Date(Date.now() - 79200000) },
        { prompt: createdPrompts[2]._id, user: allUsers[1]._id, timestamp: new Date(Date.now() - 75600000) },
        { prompt: createdPrompts[2]._id, user: allUsers[3]._id, timestamp: new Date(Date.now() - 72000000) },
        { prompt: createdPrompts[3]._id, user: allUsers[2]._id, timestamp: new Date(Date.now() - 68400000) },
        { prompt: createdPrompts[4]._id, user: allUsers[0]._id, timestamp: new Date(Date.now() - 64800000) },
        { prompt: createdPrompts[5]._id, user: allUsers[1]._id, timestamp: new Date(Date.now() - 61200000) },
      ],
      search_queries: [
        { query: 'react component', user: allUsers[1]._id, timestamp: new Date(Date.now() - 86400000) },
        { query: 'code review', user: allUsers[2]._id, timestamp: new Date(Date.now() - 82800000) },
        { query: 'summarize article', user: allUsers[0]._id, timestamp: new Date(Date.now() - 79200000) },
        { query: 'midjourney portrait', user: allUsers[3]._id, timestamp: new Date(Date.now() - 75600000) },
        { query: 'typescript', user: allUsers[1]._id, timestamp: new Date(Date.now() - 72000000) },
        { query: 'blog writing', user: allUsers[2]._id, timestamp: new Date(Date.now() - 68400000) },
        { query: 'api design', user: allUsers[0]._id, timestamp: new Date(Date.now() - 64800000) },
        { query: 'architecture visualization', user: allUsers[3]._id, timestamp: new Date(Date.now() - 61200000) },
      ],
      rating_activities: [
        { prompt: createdPrompts[0]._id, user: allUsers[1]._id, value: 1, timestamp: new Date(Date.now() - 86400000) },
        { prompt: createdPrompts[0]._id, user: allUsers[2]._id, value: 1, timestamp: new Date(Date.now() - 82800000) },
        { prompt: createdPrompts[1]._id, user: allUsers[0]._id, value: 1, timestamp: new Date(Date.now() - 79200000) },
        { prompt: createdPrompts[2]._id, user: allUsers[1]._id, value: 1, timestamp: new Date(Date.now() - 75600000) },
        { prompt: createdPrompts[3]._id, user: allUsers[3]._id, value: 1, timestamp: new Date(Date.now() - 72000000) },
        { prompt: createdPrompts[4]._id, user: allUsers[2]._id, value: 1, timestamp: new Date(Date.now() - 68400000) },
        { prompt: createdPrompts[5]._id, user: allUsers[0]._id, value: 1, timestamp: new Date(Date.now() - 64800000) },
        { prompt: createdPrompts[6]._id, user: allUsers[1]._id, value: 1, timestamp: new Date(Date.now() - 61200000) },
      ]
    };

    const analytics = await Analytics.create(analyticsData);
    console.log('Created analytics data');

    console.log('\n🎉 Database seeded successfully!');
    console.log('📊 Summary:');
    console.log(`   • Categories: ${createdCategories.length}`);
    console.log(`   • Users: ${allUsers.length} (including admin)`);
    console.log(`   • Prompts: ${createdPrompts.length}`);
    console.log(`   • Analytics entries: ${analyticsData.prompt_views.length + analyticsData.search_queries.length + analyticsData.rating_activities.length}`);
    console.log('\n🔐 Admin Login:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: snsn1133');
    console.log('\n🌐 Access the app at: http://localhost:3000');

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
