const mongoose = require('mongoose');
require('dotenv').config();

// We need to import the model differently for Node.js
const path = require('path');

async function createAdmin() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Define the User schema directly here since we can't easily import TS models
    const UserSchema = new mongoose.Schema({
      email: { type: String, required: true, unique: true },
      name: { type: String, required: true },
      password: { type: String, required: true },
      role: { type: String, enum: ['user', 'admin', 'moderator', 'super admin'], default: 'user' },
      activity_log: [
        {
          action: String,
          timestamp: Date,
        },
      ],
    }, { timestamps: true });

    // Hash password before saving
    const bcrypt = require('bcryptjs');
    UserSchema.pre('save', async function(next) {
      if (!this.isModified('password')) return next();
      
      try {
        const salt = await bcrypt.genSalt(12);
        this.password = await bcrypt.hash(this.password, salt);
        next();
      } catch (error) {
        next(error);
      }
    });

    const User = mongoose.models.User || mongoose.model('User', UserSchema);

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('Admin user already exists');
      process.exit(0);
    }

    // Create admin user
    const adminUser = await User.create({
      email: '<EMAIL>',
      name: 'Admin User',
      password: 'snsn1133',
      role: 'admin',
      activity_log: []
    });

    console.log('Admin user created successfully:', {
      email: adminUser.email,
      name: adminUser.name,
      role: adminUser.role
    });

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdmin();
