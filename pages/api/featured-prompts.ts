import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../lib/mongodb';
import Prompt from '../../models/Prompt';
import Category from '../../models/Category';
import User from '../../models/User';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await dbConnect();

    if (req.method === 'GET') {
      try {
        // First, let's check if there are any featured prompts at all
        const featuredCount = await Prompt.countDocuments({ featured: true });
        console.log('Featured prompts count:', featuredCount);

        // Get featured prompts, limit to 4
        const featuredPrompts = await Prompt.find({ featured: true, verified: true })
          .sort({ createdAt: -1 })
          .limit(4);

        // Manually populate category and user data
        const populatedPrompts = await Promise.all(
          featuredPrompts.map(async (prompt) => {
            const category = await Category.findById(prompt.category);
            const user = await User.findById(prompt.created_by, 'name');
            
            return {
              ...prompt.toObject(),
              category: category ? {
                _id: category._id,
                name: category.name,
                platform: category.platform,
                description: category.description
              } : null,
              created_by: user ? {
                _id: user._id,
                name: user.name
              } : null
            };
          })
        );

        console.log('Found featured prompts:', populatedPrompts.length);
        res.status(200).json(populatedPrompts);
      } catch (error) {
        console.error('Error fetching featured prompts:', error);
        res.status(500).json({ message: 'Failed to fetch featured prompts', error: error instanceof Error ? error.message : 'Unknown error' });
      }
    } else {
      res.setHeader('Allow', ['GET']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('Database connection error:', error);
    res.status(500).json({ message: 'Database connection failed', error: error instanceof Error ? error.message : 'Unknown error' });
  }
} 