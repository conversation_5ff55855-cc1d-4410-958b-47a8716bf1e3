import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Analytics from '../../../models/Analytics';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session || !['admin', 'moderator', 'super admin'].includes(session.user.role)) {
    return res.status(403).json({ error: 'Forbidden' });
  }
  await dbConnect();
  const analytics = await Analytics.findOne();
  res.status(200).json(analytics);
} 