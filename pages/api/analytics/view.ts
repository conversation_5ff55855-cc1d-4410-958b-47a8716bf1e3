import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/mongodb';
import Analytics from '../../../models/Analytics';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
  await dbConnect();
  const { prompt, user } = req.body;
  let analytics = await Analytics.findOne();
  if (!analytics) analytics = await Analytics.create({});
  analytics.prompt_views.push({ prompt, user, timestamp: new Date() });
  await analytics.save();
  res.status(200).json({ success: true });
} 