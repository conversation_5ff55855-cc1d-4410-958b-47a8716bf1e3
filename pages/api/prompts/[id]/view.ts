import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import dbConnect from '../../../../lib/mongodb';
import Analytics from '../../../../models/Analytics';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { id } = req.query;

  try {
    await dbConnect();

    // Log view in analytics
    let analytics = await Analytics.findOne();
    if (!analytics) {
      analytics = new Analytics({
        prompt_views: [],
        search_queries: [],
        rating_activities: []
      });
    }

    // Check if user has already viewed this prompt recently (within last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentView = analytics.prompt_views.find(
      (view: any) => 
        view.prompt.toString() === id &&
        view.user.toString() === session.user.id &&
        view.timestamp > oneHourAgo
    );

    if (!recentView) {
      analytics.prompt_views.push({
        prompt: id,
        user: session.user.id,
        timestamp: new Date()
      });

      await analytics.save();
    }

    res.status(200).json({ message: 'View logged successfully' });
  } catch (error) {
    console.error('Error logging view:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
