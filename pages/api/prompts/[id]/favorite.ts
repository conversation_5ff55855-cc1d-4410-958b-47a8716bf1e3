import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import dbConnect from '../../../../lib/mongodb';
import Favorite from '../../../../models/Favorite';
import Prompt from '../../../../models/Prompt';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ message: 'Invalid prompt ID' });
  }

  try {
    await dbConnect();

    // Verify prompt exists
    const prompt = await Prompt.findById(id);
    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    if (req.method === 'POST') {
      // Add to favorites
      try {
        const favorite = new Favorite({
          user: session.user.id,
          prompt: id
        });
        
        await favorite.save();
        
        res.status(201).json({ 
          message: 'Added to favorites',
          isFavorited: true
        });
      } catch (error: any) {
        // Handle duplicate key error (already favorited)
        if (error.code === 11000) {
          return res.status(200).json({ 
            message: 'Already in favorites',
            isFavorited: true
          });
        }
        throw error;
      }
    } else if (req.method === 'DELETE') {
      // Remove from favorites
      const result = await Favorite.findOneAndDelete({
        user: session.user.id,
        prompt: id
      });

      if (!result) {
        return res.status(404).json({ message: 'Favorite not found' });
      }

      res.status(200).json({ 
        message: 'Removed from favorites',
        isFavorited: false
      });
    } else if (req.method === 'GET') {
      // Check if favorited
      const favorite = await Favorite.findOne({
        user: session.user.id,
        prompt: id
      });

      res.status(200).json({ 
        isFavorited: !!favorite
      });
    } else {
      res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error handling favorite:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
