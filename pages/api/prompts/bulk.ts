import type { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import xlsx from 'xlsx';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Prompt from '../../../models/Prompt';
import Category from '../../../models/Category';
import User from '../../../models/User';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();

  if (req.method === 'GET') {
    // Generate and download Excel template
    return generateTemplate(req, res);
  } else if (req.method === 'POST') {
    // Handle bulk upload
    return handleBulkUpload(req, res);
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

async function generateTemplate(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get categories and platforms for the template
    const categories = await Category.find().sort({ platform: 1, name: 1 });

    // Get valid platforms
    const platformsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/platforms`);
    const platformsData = await platformsResponse.json();
    const validPlatforms = platformsData.platforms || ['ChatGPT', 'GitHub Copilot', 'Midjourney'];

    // Create workbook
    const workbook = xlsx.utils.book_new();

    // Create main template sheet
    const templateData = [
      // Headers
      ['title', 'description', 'prompt_text', 'platform', 'category_name', 'tags'],
      // Sample data
      [
        'Article Summarizer',
        'Summarize long articles into key points with actionable insights',
        'Please summarize the following article into 3-5 key points, highlighting the most important insights and actionable takeaways:\n\n[ARTICLE_TEXT]\n\nFormat your response as:\n• Key Point 1: [insight]\n• Key Point 2: [insight]\n• Key Point 3: [insight]\n• Action Items: [what readers should do]',
        'ChatGPT',
        'Summarization',
        'summary,article,key-points,insights'
      ],
      [
        'React Component Generator',
        'Generate React functional components with TypeScript and best practices',
        'Create a React functional component with the following specifications:\n- Component name: [COMPONENT_NAME]\n- TypeScript with proper interfaces\n- Props validation\n- useState and useEffect hooks as needed\n- Responsive design with Tailwind CSS\n- Accessibility features (ARIA labels, keyboard navigation)\n- Error boundary handling\n- Loading states\n- JSDoc comments for documentation',
        'GitHub Copilot',
        'Code Generation',
        'react,typescript,component,frontend'
      ],
      [
        'Fantasy Character Portrait',
        'Create detailed fantasy character portraits with specific art style',
        'Create a detailed portrait of a [CHARACTER_TYPE] with [PHYSICAL_DESCRIPTION], wearing [CLOTHING/ARMOR], in [ART_STYLE] style, with [LIGHTING] lighting, [BACKGROUND] background, highly detailed, professional digital art, 8k resolution',
        'Midjourney',
        'Character Design',
        'fantasy,character,portrait,digital-art'
      ]
    ];

    const templateSheet = xlsx.utils.aoa_to_sheet(templateData);

    // Set column widths
    templateSheet['!cols'] = [
      { wch: 25 }, // title
      { wch: 40 }, // description
      { wch: 60 }, // prompt_text
      { wch: 15 }, // platform
      { wch: 20 }, // category_name
      { wch: 30 }  // tags
    ];

    xlsx.utils.book_append_sheet(workbook, templateSheet, 'Prompts Template');

    // Create categories reference sheet
    const categoryData = [
      ['platform', 'category_name', 'description'],
      ...categories.map(cat => [cat.platform, cat.name, cat.description || ''])
    ];

    const categorySheet = xlsx.utils.aoa_to_sheet(categoryData);
    categorySheet['!cols'] = [
      { wch: 15 }, // platform
      { wch: 25 }, // category_name
      { wch: 40 }  // description
    ];

    xlsx.utils.book_append_sheet(workbook, categorySheet, 'Available Categories');

    // Create instructions sheet
    const instructionsData = [
      ['Bulk Upload Instructions'],
      [''],
      ['Required Fields:'],
      ['• title: The prompt title (required)'],
      ['• description: Brief description of what the prompt does (required)'],
      ['• prompt_text: The actual prompt content (required)'],
      [`• platform: Must be one of: ${validPlatforms.join(', ')} (required)`],
      ['• category_name: Must match an existing category name (required)'],
      ['• tags: Comma-separated list of tags (optional)'],
      [''],
      ['Important Notes:'],
      ['• Use the "Available Categories" sheet to see valid category names'],
      ['• Category names must match exactly (case-sensitive)'],
      ['• Tags should be separated by commas without spaces'],
      ['• All prompts will be created as unverified and require admin approval'],
      ['• Maximum file size: 10MB'],
      ['• Maximum rows: 1000 prompts per upload'],
      [''],
      ['Platform Guidelines:'],
      ['• ChatGPT: Text-based prompts for conversation and analysis'],
      ['• GitHub Copilot: Code generation and development prompts'],
      ['• Midjourney: Image generation and artistic prompts'],
      [''],
      ['Tips for Success:'],
      ['• Test with a small batch first (5-10 prompts)'],
      ['• Keep descriptions concise but informative'],
      ['• Use clear, actionable prompt text'],
      ['• Include relevant tags for better discoverability']
    ];

    const instructionsSheet = xlsx.utils.aoa_to_sheet(instructionsData);
    instructionsSheet['!cols'] = [{ wch: 80 }];

    xlsx.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

    // Generate buffer
    const buffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="prompt-bulk-upload-template.xlsx"');
    res.setHeader('Content-Length', buffer.length);

    res.send(buffer);
  } catch (error) {
    console.error('Template generation error:', error);
    res.status(500).json({ error: 'Failed to generate template', details: error });
  }
}

async function handleBulkUpload(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user exists and get user data
    const user = await User.findById(session.user.id);
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    const form = new formidable.IncomingForm({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
      keepExtensions: true,
    });

    form.parse(req, async (err, fields, files) => {
      if (err) {
        console.error('File upload error:', err);
        return res.status(400).json({ error: 'File upload error', details: err.message });
      }

      const file = Array.isArray(files.file) ? files.file[0] : files.file;
      if (!file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      try {
        // Read Excel file
        const workbook = xlsx.readFile((file as any).filepath);
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const rawData = xlsx.utils.sheet_to_json(sheet);

        if (!rawData || rawData.length === 0) {
          return res.status(400).json({ error: 'Excel file is empty or invalid' });
        }

        if (rawData.length > 1000) {
          return res.status(400).json({ error: 'Maximum 1000 prompts allowed per upload' });
        }

        // Get all categories for validation
        const categories = await Category.find();
        const categoryMap = new Map(categories.map(cat => [cat.name, cat._id]));

        // Get valid platforms
        const platformsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/platforms`);
        const platformsData = await platformsResponse.json();
        const validPlatforms = platformsData.platforms || ['ChatGPT', 'GitHub Copilot', 'Midjourney'];

        const results: {
          successful: Array<{
            row: number;
            prompt: {
              _id: string;
              title: string;
              platform: string;
              category_name: string;
            };
          }>;
          failed: Array<{
            row: number;
            data: any;
            errors: string[];
          }>;
          total: number;
        } = {
          successful: [],
          failed: [],
          total: rawData.length
        };

        // Process each row
        for (let i = 0; i < rawData.length; i++) {
          const row = rawData[i] as any;
          const rowNumber = i + 2; // +2 because Excel is 1-indexed and we have headers

          try {
            // Validate required fields
            const validation = validatePromptData(row, categoryMap, validPlatforms);
            if (!validation.isValid) {
              results.failed.push({
                row: rowNumber,
                data: row,
                errors: validation.errors
              });
              continue;
            }

            // Create prompt data
            const promptData = {
              title: row.title.trim(),
              description: row.description.trim(),
              prompt_text: row.prompt_text.trim(),
              platform: row.platform.trim(),
              category: categoryMap.get(row.category_name.trim()),
              tags: row.tags ? row.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
              created_by: user._id,
              verified: false, // All bulk uploads start as unverified
              ratings: []
            };

            // Create the prompt
            const prompt = await Prompt.create(promptData);
            results.successful.push({
              row: rowNumber,
              prompt: {
                _id: prompt._id,
                title: prompt.title,
                platform: prompt.platform,
                category_name: row.category_name.trim()
              }
            });

          } catch (error) {
            console.error(`Error creating prompt at row ${rowNumber}:`, error);
            results.failed.push({
              row: rowNumber,
              data: row,
              errors: [`Database error: ${error instanceof Error ? error.message : 'Unknown error'}`]
            });
          }
        }

        // Return detailed results
        res.status(200).json({
          message: `Processed ${results.total} prompts. ${results.successful.length} successful, ${results.failed.length} failed.`,
          results
        });

      } catch (error) {
        console.error('Excel processing error:', error);
        res.status(400).json({
          error: 'Failed to process Excel file',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

  } catch (error) {
    console.error('Bulk upload error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

function validatePromptData(row: any, categoryMap: Map<string, any>, validPlatforms: string[]) {
  const errors: string[] = [];

  // Check required fields
  if (!row.title || typeof row.title !== 'string' || !row.title.trim()) {
    errors.push('Title is required');
  } else if (row.title.trim().length > 200) {
    errors.push('Title must be less than 200 characters');
  }

  if (!row.description || typeof row.description !== 'string' || !row.description.trim()) {
    errors.push('Description is required');
  } else if (row.description.trim().length > 500) {
    errors.push('Description must be less than 500 characters');
  }

  if (!row.prompt_text || typeof row.prompt_text !== 'string' || !row.prompt_text.trim()) {
    errors.push('Prompt text is required');
  } else if (row.prompt_text.trim().length > 5000) {
    errors.push('Prompt text must be less than 5000 characters');
  }

  // Validate platform
  if (!row.platform || !validPlatforms.includes(row.platform.trim())) {
    errors.push(`Platform must be one of: ${validPlatforms.join(', ')}`);
  }

  // Validate category
  if (!row.category_name || typeof row.category_name !== 'string' || !row.category_name.trim()) {
    errors.push('Category name is required');
  } else if (!categoryMap.has(row.category_name.trim())) {
    errors.push(`Invalid category name: "${row.category_name.trim()}". Check the Available Categories sheet.`);
  }

  // Validate tags (optional)
  if (row.tags && typeof row.tags === 'string') {
    const tags = row.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
    if (tags.length > 10) {
      errors.push('Maximum 10 tags allowed');
    }
    for (const tag of tags) {
      if (tag.length > 50) {
        errors.push(`Tag "${tag}" is too long (max 50 characters)`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}