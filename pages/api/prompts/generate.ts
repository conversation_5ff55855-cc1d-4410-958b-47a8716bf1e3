import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

interface GenerateRequest {
  purpose: string;
  goal: string;
  audience: string;
  platform: string;
  context: string;
  tone: string;
  inputType: string;
  inputExample?: string;
  outputFormat: string;
  outputExample?: string;
}

interface GenerateResponse {
  title: string;
  description: string;
  prompt_text: string;
  instructions: string;
  example: string;
  tags: string[];
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const {
      purpose,
      goal,
      audience,
      platform,
      context,
      tone,
      inputType,
      inputExample,
      outputFormat,
      outputExample
    }: GenerateRequest = req.body;

    // Validate required fields
    if (!purpose || !goal || !audience || !platform || !context || !tone || !inputType || !outputFormat) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Generate the prompt based on user inputs
    const generatedPrompt = generatePrompt({
      purpose,
      goal,
      audience,
      platform,
      context,
      tone,
      inputType,
      inputExample,
      outputFormat,
      outputExample
    });

    res.status(200).json(generatedPrompt);
  } catch (error) {
    console.error('Generate prompt error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

function generatePrompt(data: GenerateRequest): GenerateResponse {
  const {
    purpose,
    goal,
    audience,
    platform,
    context,
    tone,
    inputType,
    inputExample,
    outputFormat,
    outputExample
  } = data;

  // Create a structured prompt based on the inputs
  let promptText = '';
  let instructions = '';
  let example = '';
  let title = '';
  let description = '';
  let tags: string[] = [];

  // Generate title
  title = `${purpose.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Assistant for ${audience}`;

  // Generate description
  description = `A specialized prompt designed to help with ${purpose.replace('-', ' ')} tasks. ${goal}`;

  // Build the main prompt text
  promptText += `You are a ${tone} AI assistant specialized in ${purpose.replace('-', ' ')}. `;
  promptText += `Your goal is to ${goal.toLowerCase()}. `;
  promptText += `You are helping ${audience.toLowerCase()}. `;

  if (context) {
    promptText += `\n\nContext: ${context}`;
  }

  // Add input handling
  if (inputType !== 'none') {
    promptText += `\n\nThe user will provide ${inputType.replace('-', ' ')} as input. `;
    if (inputExample) {
      promptText += `For example: "${inputExample}"`;
    }
  }

  // Add output format requirements
  promptText += `\n\nPlease respond in ${outputFormat.replace('-', ' ')} format. `;
  
  if (outputExample) {
    promptText += `The output should be similar to: "${outputExample}"`;
  }

  // Add platform-specific instructions
  switch (platform) {
    case 'ChatGPT':
      promptText += '\n\nBe conversational and helpful. Ask clarifying questions if needed.';
      break;
    case 'GitHub Copilot':
      promptText += '\n\nFocus on code quality, best practices, and clear comments.';
      break;
    case 'Microsoft Copilot (Office)':
      promptText += '\n\nBe professional and efficient. Focus on productivity and clear business communication.';
      break;
    case 'Midjourney':
      promptText += '\n\nBe descriptive and specific about visual elements, style, and composition.';
      break;
  }

  // Generate instructions
  instructions = `This prompt is designed for ${platform} to help with ${purpose.replace('-', ' ')} tasks. `;
  instructions += `Use a ${tone} tone and focus on providing ${outputFormat.replace('-', ' ')} responses. `;
  if (context) {
    instructions += `Consider the following context: ${context}`;
  }

  // Generate example
  if (inputExample && outputExample) {
    example = `Input: ${inputExample}\n\nOutput: ${outputExample}`;
  } else if (inputExample) {
    example = `Example input: ${inputExample}`;
  } else if (outputExample) {
    example = `Example output: ${outputExample}`;
  } else {
    example = generateDefaultExample(purpose, platform, outputFormat);
  }

  // Generate tags
  tags = [
    purpose.replace('-', ' '),
    platform.toLowerCase().replace(' ', '-'),
    tone,
    outputFormat.replace('-', ' '),
    ...audience.toLowerCase().split(' ').slice(0, 2)
  ].filter(tag => tag.length > 2);

  return {
    title,
    description,
    prompt_text: promptText.trim(),
    instructions,
    example,
    tags
  };
}

function generateDefaultExample(purpose: string, platform: string, outputFormat: string): string {
  const examples: { [key: string]: string } = {
    'content-creation': 'Input: "Write a blog post about sustainable living"\nOutput: A well-structured blog post with introduction, main points, and conclusion',
    'code-assistance': 'Input: "Create a function to validate email addresses"\nOutput: Clean, commented code with error handling',
    'data-analysis': 'Input: "Analyze sales data trends"\nOutput: Clear insights with key findings and recommendations',
    'creative-writing': 'Input: "Write a short story about time travel"\nOutput: Engaging narrative with character development and plot',
    'problem-solving': 'Input: "How to improve team productivity"\nOutput: Actionable solutions with implementation steps',
    'learning-education': 'Input: "Explain quantum physics concepts"\nOutput: Clear explanations with examples and analogies',
    'business-strategy': 'Input: "Develop a marketing strategy"\nOutput: Comprehensive strategy with tactics and metrics',
    'image-generation': 'Input: "Create a logo for a tech startup"\nOutput: Detailed visual description with style specifications'
  };

  return examples[purpose] || 'Input: [Your specific request]\nOutput: [Relevant response in the specified format]';
}
