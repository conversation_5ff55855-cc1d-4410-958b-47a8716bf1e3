import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import dbConnect from '../../../../lib/mongodb';
import Prompt from '../../../../models/Prompt';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  try {
    // Check authentication and authorization
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user is admin or moderator
    if (session.user.role !== 'admin' && session.user.role !== 'moderator') {
      return res.status(403).json({ error: 'Admin or moderator access required' });
    }

    await dbConnect();

  if (req.method === 'PATCH') {
    try {
      const { verified, ...otherUpdates } = req.body;

      // Build update object
      const updateData: any = {};

      // Handle verification status (both admin and moderator can verify)
      if (typeof verified === 'boolean') {
        updateData.verified = verified;
      }

      // Allow admins to update other fields, moderators can only verify
      if (session.user.role === 'admin') {
        Object.assign(updateData, otherUpdates);
      }

      const updatedPrompt = await Prompt.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      )
        .populate('category', 'name')
        .populate('created_by', 'name email');

      if (!updatedPrompt) {
        return res.status(404).json({ error: 'Prompt not found' });
      }

      res.status(200).json(updatedPrompt);
    } catch (error) {
      console.error('Error updating prompt:', error);
      res.status(500).json({ error: 'Failed to update prompt' });
    }
  } else if (req.method === 'DELETE') {
    try {
      // Only admins can delete prompts
      if (session.user.role !== 'admin') {
        return res.status(403).json({ error: 'Admin access required for deletion' });
      }

      const deletedPrompt = await Prompt.findByIdAndDelete(id);

      if (!deletedPrompt) {
        return res.status(404).json({ error: 'Prompt not found' });
      }

      res.status(200).json({ message: 'Prompt deleted successfully' });
    } catch (error) {
      console.error('Error deleting prompt:', error);
      res.status(500).json({ error: 'Failed to delete prompt' });
    }
  } else {
    res.setHeader('Allow', ['PATCH', 'DELETE']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  } catch (error) {
    console.error('Admin prompts API error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred'
    });
  }
}
