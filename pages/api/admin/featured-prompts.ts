import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Prompt from '../../../models/Prompt';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();

  // Check authentication and admin role
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id || (session.user?.role !== 'admin' && session.user?.role !== 'moderator')) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (req.method === 'GET') {
    try {
      // Get all featured prompts
      const featuredPrompts = await Prompt.find({ featured: true })
        .populate('category')
        .populate('created_by', 'name')
        .sort({ createdAt: -1 });

      res.status(200).json(featuredPrompts);
    } catch (error) {
      console.error('Error fetching featured prompts:', error);
      res.status(500).json({ message: 'Failed to fetch featured prompts' });
    }
  } else if (req.method === 'POST') {
    try {
      const { promptId, featured } = req.body;

      if (!promptId) {
        return res.status(400).json({ message: 'Prompt ID is required' });
      }

      if (typeof featured !== 'boolean') {
        return res.status(400).json({ message: 'Featured status must be a boolean' });
      }

      // Update the prompt's featured status
      const updatedPrompt = await Prompt.findByIdAndUpdate(
        promptId,
        { featured },
        { new: true }
      ).populate('category')
       .populate('created_by', 'name');

      if (!updatedPrompt) {
        return res.status(404).json({ message: 'Prompt not found' });
      }

      res.status(200).json(updatedPrompt);
    } catch (error) {
      console.error('Error updating featured status:', error);
      res.status(500).json({ message: 'Failed to update featured status' });
    }
  } else if (req.method === 'PUT') {
    try {
      const { promptIds } = req.body;

      if (!Array.isArray(promptIds) || promptIds.length > 4) {
        return res.status(400).json({ message: 'Must provide an array of up to 4 prompt IDs' });
      }

      // First, unfeature all prompts
      await Prompt.updateMany({}, { featured: false });

      // Then, feature the selected prompts
      if (promptIds.length > 0) {
        await Prompt.updateMany(
          { _id: { $in: promptIds } },
          { featured: true }
        );
      }

      // Get the updated featured prompts
      const featuredPrompts = await Prompt.find({ featured: true })
        .populate('category')
        .populate('created_by', 'name')
        .sort({ createdAt: -1 });

      res.status(200).json(featuredPrompts);
    } catch (error) {
      console.error('Error updating featured prompts:', error);
      res.status(500).json({ message: 'Failed to update featured prompts' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST', 'PUT']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 