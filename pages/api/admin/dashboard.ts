import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Prompt from '../../../models/Prompt';
import User from '../../../models/User';
import Category from '../../../models/Category';
import Analytics from '../../../models/Analytics';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.role || (session.user.role !== 'admin' && session.user.role !== 'moderator')) {
    return res.status(403).json({ message: 'Admin or moderator access required' });
  }

  try {
    await dbConnect();

    // Get basic counts
    const [totalPrompts, totalUsers, totalCategories] = await Promise.all([
      Prompt.countDocuments(),
      User.countDocuments(),
      Category.countDocuments(),
    ]);

    // Get verified prompts count
    const verifiedPrompts = await Prompt.countDocuments({ verified: true });
    const pendingPrompts = totalPrompts - verifiedPrompts;
    
    // Get featured prompts count
    const featuredPrompts = await Prompt.countDocuments({ featured: true });

    // Get analytics data
    const analytics = await Analytics.findOne();
    const totalViews = analytics?.prompt_views?.length || 0;
    const totalRatings = analytics?.rating_activities?.length || 0;

    // Get top performing prompts
    const topPromptsData = await Prompt.aggregate([
      {
        $addFields: {
          ratingScore: {
            $sum: {
              $map: {
                input: '$ratings',
                as: 'rating',
                in: '$$rating.value'
              }
            }
          },
          viewCount: {
            $size: {
              $ifNull: ['$views', []]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category'
        }
      },
      {
        $addFields: {
          category: { $arrayElemAt: ['$category', 0] }
        }
      },
      {
        $sort: { ratingScore: -1, viewCount: -1 }
      },
      {
        $limit: 5
      },
      {
        $project: {
          title: 1,
          platform: 1,
          ratingScore: 1,
          viewCount: 1
        }
      }
    ]);

    // Calculate view counts from analytics
    const promptViewCounts = new Map();
    if (analytics?.prompt_views) {
      analytics.prompt_views.forEach((view: any) => {
        const promptId = view.prompt.toString();
        promptViewCounts.set(promptId, (promptViewCounts.get(promptId) || 0) + 1);
      });
    }

    const topPrompts = topPromptsData.map(prompt => ({
      _id: prompt._id,
      title: prompt.title,
      platform: prompt.platform,
      rating: prompt.ratingScore || 0,
      views: promptViewCounts.get(prompt._id.toString()) || 0
    }));

    // Get recent activity
    const recentActivity = [];
    
    // Recent prompts
    const recentPrompts = await Prompt.find()
      .populate('created_by', 'name')
      .sort({ createdAt: -1 })
      .limit(3);
    
    recentPrompts.forEach(prompt => {
      recentActivity.push({
        type: 'prompt_created',
        description: `New prompt "${prompt.title}" created by ${prompt.created_by?.name}`,
        timestamp: prompt.createdAt,
        user: prompt.created_by?.name
      });
    });

    // Recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(3);
    
    recentUsers.forEach(user => {
      recentActivity.push({
        type: 'user_registered',
        description: `New user ${user.name} registered`,
        timestamp: user.createdAt,
        user: user.name
      });
    });

    // Recent ratings from analytics
    if (analytics?.rating_activities) {
      const recentRatings = analytics.rating_activities
        .slice(-3)
        .reverse();
      
      for (const rating of recentRatings) {
        try {
          const [prompt, user] = await Promise.all([
            Prompt.findById(rating.prompt).select('title'),
            User.findById(rating.user).select('name')
          ]);
          
          if (prompt && user) {
            recentActivity.push({
              type: 'rating',
              description: `${user.name} ${rating.value > 0 ? 'upvoted' : 'downvoted'} "${prompt.title}"`,
              timestamp: rating.timestamp,
              user: user.name
            });
          }
        } catch (err) {
          // Skip if prompt or user not found
        }
      }
    }

    // Sort recent activity by timestamp and limit
    recentActivity.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    const limitedRecentActivity = recentActivity.slice(0, 8);

    const dashboardStats = {
      totalPrompts,
      totalUsers,
      totalCategories,
      verifiedPrompts,
      pendingPrompts,
      totalViews,
      totalRatings,
      featuredPrompts,
      topPrompts,
      recentActivity: limitedRecentActivity
    };

    res.status(200).json(dashboardStats);
  } catch (error) {
    console.error('Dashboard API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
