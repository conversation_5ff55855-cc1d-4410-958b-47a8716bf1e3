import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from './auth/[...nextauth]';
import dbConnect from '../../lib/mongodb';
import Platform from '../../models/Platform';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();

  // Check authentication for write operations
  if (req.method !== 'GET') {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user is admin
    if (session.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin privileges required' });
    }
  }

  switch (req.method) {
    case 'GET':
      try {
        const platforms = await Platform.find().sort({ name: 1 });
        const platformNames = platforms.map(p => p.name);

        // If no platforms exist, create default ones
        if (platformNames.length === 0) {
          const defaultPlatforms = ['ChatGPT', 'GitHub Copilot', 'Midjourney'];
          await Platform.insertMany(defaultPlatforms.map(name => ({ name })));
          return res.status(200).json({ platforms: defaultPlatforms });
        }

        return res.status(200).json({ platforms: platformNames });
      } catch (error) {
        console.error('Error fetching platforms:', error);
        return res.status(500).json({ error: 'Failed to fetch platforms' });
      }

    case 'POST':
      try {
        const { platform } = req.body;

        if (!platform || typeof platform !== 'string') {
          return res.status(400).json({ error: 'Platform name is required' });
        }

        const trimmedPlatform = platform.trim();

        // Check if platform already exists
        const existingPlatform = await Platform.findOne({ name: trimmedPlatform });
        if (existingPlatform) {
          return res.status(400).json({ error: 'Platform already exists' });
        }

        // Create new platform
        await Platform.create({ name: trimmedPlatform });

        // Return updated list
        const platforms = await Platform.find().sort({ name: 1 });
        const platformNames = platforms.map(p => p.name);

        return res.status(201).json({ platforms: platformNames });
      } catch (error) {
        console.error('Error creating platform:', error);
        return res.status(500).json({ error: 'Failed to create platform' });
      }

    case 'DELETE':
      try {
        const { platform: platformToDelete } = req.body;

        if (!platformToDelete || typeof platformToDelete !== 'string') {
          return res.status(400).json({ error: 'Platform name is required' });
        }

        // Delete the platform
        const result = await Platform.deleteOne({ name: platformToDelete });

        if (result.deletedCount === 0) {
          return res.status(404).json({ error: 'Platform not found' });
        }

        // Return updated list
        const platforms = await Platform.find().sort({ name: 1 });
        const platformNames = platforms.map(p => p.name);

        return res.status(200).json({ platforms: platformNames });
      } catch (error) {
        console.error('Error deleting platform:', error);
        return res.status(500).json({ error: 'Failed to delete platform' });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
      return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
