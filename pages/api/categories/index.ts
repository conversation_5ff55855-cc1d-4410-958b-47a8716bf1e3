import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/mongodb';
import Category from '../../../models/Category';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await dbConnect();
    if (req.method === 'GET') {
      const categories = await Category.find();
      res.status(200).json(categories);
    } else if (req.method === 'POST') {
    try {
      const category = await Category.create(req.body);
      res.status(201).json(category);
    } catch (err) {
      res.status(400).json({ error: 'Failed to create category', details: err });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
  } catch (error) {
    console.error('Categories API error:', error);

    // Return mock categories when database is unavailable
    if (req.method === 'GET') {
      const mockCategories = [
        { _id: 'cat1', name: 'Writing', platform: 'ChatGPT' },
        { _id: 'cat2', name: 'Development', platform: 'GitHub Copilot' },
        { _id: 'cat3', name: 'Art', platform: 'Midjourney' },
        { _id: 'cat4', name: 'Analysis', platform: 'ChatGPT' },
        { _id: 'cat5', name: 'Translation', platform: 'ChatGPT' }
      ];
      res.status(200).json(mockCategories);
    } else {
      res.status(500).json({ error: 'Database connection failed' });
    }
  }
}