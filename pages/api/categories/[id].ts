import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/mongodb';
import Category from '../../../models/Category';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();
  const { id } = req.query;
  if (req.method === 'PUT') {
    try {
      const category = await Category.findByIdAndUpdate(id, req.body, { new: true });
      if (!category) return res.status(404).json({ error: 'Category not found' });
      res.status(200).json(category);
    } catch (err) {
      res.status(400).json({ error: 'Failed to update category', details: err });
    }
  } else if (req.method === 'DELETE') {
    try {
      const category = await Category.findByIdAndDelete(id);
      if (!category) return res.status(404).json({ error: 'Category not found' });
      res.status(204).end();
    } catch (err) {
      res.status(400).json({ error: 'Failed to delete category', details: err });
    }
  } else {
    res.setHeader('Allow', ['PUT', 'DELETE']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 