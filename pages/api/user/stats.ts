import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Prompt from '../../../models/Prompt';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    await dbConnect();

    // Get user's prompts with ratings
    const userPrompts = await Prompt.find({ created_by: session.user.id })
      .populate('ratings')
      .lean();

    // Calculate statistics
    const totalPrompts = userPrompts.length;
    
    // Calculate total views (sum of all view counts)
    const totalViews = userPrompts.reduce((sum, prompt: any) => {
      return sum + (prompt.view_count || 0);
    }, 0);

    // Calculate total likes (positive ratings)
    let totalLikes = 0;
    let totalRatings = 0;
    let ratingSum = 0;

    userPrompts.forEach((prompt: any) => {
      if (prompt.ratings && Array.isArray(prompt.ratings)) {
        prompt.ratings.forEach((rating: any) => {
          totalRatings++;
          ratingSum += rating.value;
          if (rating.value > 0) {
            totalLikes++;
          }
        });
      }
    });

    // Calculate average rating
    const averageRating = totalRatings > 0 ? ratingSum / totalRatings : 0;

    // Get verified prompts count
    const verifiedPrompts = userPrompts.filter((prompt: any) => prompt.verified).length;

    // Calculate this month's activity
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);

    const thisMonthPrompts = userPrompts.filter((prompt: any) => 
      new Date(prompt.createdAt) >= thisMonth
    ).length;

    const stats = {
      totalPrompts,
      totalViews,
      totalLikes,
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
      verifiedPrompts,
      thisMonthPrompts,
      totalRatings,
      // Additional stats for achievements
      popularPrompts: userPrompts.filter((prompt: any) => (prompt.view_count || 0) >= 50).length,
      highRatedPrompts: userPrompts.filter((prompt: any) => {
        if (!prompt.ratings || !Array.isArray(prompt.ratings) || prompt.ratings.length === 0) return false;
        const avgRating = prompt.ratings.reduce((sum: number, rating: any) => sum + rating.value, 0) / prompt.ratings.length;
        return avgRating >= 4;
      }).length
    };

    res.status(200).json(stats);

  } catch (error) {
    console.error('Error fetching user stats:', error);
    
    // Return mock data for development/testing
    res.status(200).json({
      totalPrompts: 0,
      totalViews: 0,
      totalLikes: 0,
      averageRating: 0,
      verifiedPrompts: 0,
      thisMonthPrompts: 0,
      totalRatings: 0,
      popularPrompts: 0,
      highRatedPrompts: 0
    });
  }
}
