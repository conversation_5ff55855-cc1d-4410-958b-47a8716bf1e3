import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Prompt from '../../../models/Prompt';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    await dbConnect();

    // Get user's prompts with populated category
    const prompts = await Prompt.find({ created_by: session.user.id })
      .populate('category', 'name')
      .sort({ createdAt: -1 }) // Most recent first
      .lean();

    // Transform the data to match the PromptCard expected format
    const transformedPrompts = prompts.map((prompt: any) => ({
      _id: prompt._id?.toString() || '',
      title: prompt.title,
      description: prompt.description,
      prompt_text: prompt.prompt_text,
      platform: prompt.platform,
      category: {
        _id: prompt.category?._id?.toString() || '',
        name: prompt.category?.name || 'Uncategorized'
      },
      tags: prompt.tags || [],
      ratings: prompt.ratings || [], // Keep the original ratings array for PromptCard
      verified: prompt.verified || false,
      created_by: {
        _id: session.user.id,
        name: session.user.name || 'Anonymous'
      },
      createdAt: prompt.createdAt
    }));

    res.status(200).json(transformedPrompts);
  } catch (error) {
    console.error('Error fetching user prompts:', error);
    res.status(500).json({ 
      error: 'Failed to fetch prompts',
      message: 'Internal server error'
    });
  }
}
