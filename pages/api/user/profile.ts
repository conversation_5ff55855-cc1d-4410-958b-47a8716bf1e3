import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import User from '../../../models/User';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  await dbConnect();

  if (req.method === 'GET') {
    try {
      const user = await User.findById(session.user.id).select('-password');
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Ensure preferences exist with defaults
      const userWithDefaults = {
        ...user.toObject(),
        preferences: {
          emailNotifications: true,
          publicProfile: false,
          defaultPlatform: 'ChatGPT',
          ...user.preferences
        }
      };

      res.status(200).json(userWithDefaults);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else if (req.method === 'PATCH') {
    try {
      const { name, bio, preferences } = req.body;

      // Validate input
      if (!name?.trim()) {
        return res.status(400).json({ message: 'Name is required' });
      }

      const updateData: any = {
        name: name.trim(),
        bio: bio?.trim() || '',
      };

      // Update preferences if provided
      if (preferences) {
        updateData.preferences = {
          emailNotifications: preferences.emailNotifications ?? true,
          publicProfile: preferences.publicProfile ?? false,
          defaultPlatform: preferences.defaultPlatform || 'ChatGPT',
        };
      }

      const updatedUser = await User.findByIdAndUpdate(
        session.user.id,
        updateData,
        { new: true, runValidators: true }
      ).select('-password');

      if (!updatedUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Log the activity
      await User.findByIdAndUpdate(session.user.id, {
        $push: {
          activity_log: {
            action: 'profile_updated',
            timestamp: new Date(),
          }
        }
      });

      res.status(200).json(updatedUser);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'PATCH']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
