import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import dbConnect from '../../lib/mongodb';
import Favorite from '../../models/Favorite';
import Prompt from '../../models/Prompt';
import Category from '../../models/Category';
import User from '../../models/User';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    await dbConnect();

    const {
      platform,
      category,
      search,
      sort = 'newest',
      page = '1',
      limit = '9'
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Get user's favorites with populated prompt data
    let favoriteQuery = Favorite.find({ user: session.user.id })
      .populate({
        path: 'prompt',
        populate: [
          { path: 'category', select: 'name' },
          { path: 'created_by', select: 'name' }
        ]
      })
      .lean();

    const favorites = await favoriteQuery.exec();

    // Filter out favorites where prompt was deleted
    let validFavorites = favorites.filter(fav => fav.prompt);

    // Apply filters to the populated prompts
    if (platform && platform !== 'All Platforms') {
      validFavorites = validFavorites.filter(fav => 
        (fav.prompt as any).platform === platform
      );
    }

    if (category && category !== 'All Categories') {
      validFavorites = validFavorites.filter(fav => 
        (fav.prompt as any).category?.name === category
      );
    }

    if (search) {
      const searchLower = (search as string).toLowerCase();
      validFavorites = validFavorites.filter(fav => {
        const prompt = fav.prompt as any;
        return prompt.title.toLowerCase().includes(searchLower) ||
               prompt.description.toLowerCase().includes(searchLower) ||
               prompt.tags.some((tag: string) => tag.toLowerCase().includes(searchLower));
      });
    }

    // Sort favorites
    validFavorites.sort((a, b) => {
      const promptA = a.prompt as any;
      const promptB = b.prompt as any;
      
      switch (sort) {
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'rating':
          const ratingA = promptA.ratings.reduce((sum: number, r: any) => sum + r.value, 0);
          const ratingB = promptB.ratings.reduce((sum: number, r: any) => sum + r.value, 0);
          return ratingB - ratingA;
        case 'title':
          return promptA.title.localeCompare(promptB.title);
        case 'newest':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    // Apply pagination
    const totalFavorites = validFavorites.length;
    const paginatedFavorites = validFavorites.slice(skip, skip + limitNum);

    // Transform data to match PromptCard expected format
    const transformedPrompts = paginatedFavorites.map((favorite: any) => {
      const prompt = favorite.prompt;
      return {
        _id: prompt._id?.toString() || '',
        title: prompt.title,
        description: prompt.description,
        prompt_text: prompt.prompt_text,
        platform: prompt.platform,
        category: {
          _id: prompt.category?._id?.toString() || '',
          name: prompt.category?.name || 'Uncategorized'
        },
        tags: prompt.tags || [],
        ratings: prompt.ratings || [],
        verified: prompt.verified || false,
        created_by: {
          _id: prompt.created_by?._id?.toString() || '',
          name: prompt.created_by?.name || 'Anonymous'
        },
        createdAt: prompt.createdAt,
        favoritedAt: favorite.createdAt // When user favorited it
      };
    });

    const totalPages = Math.ceil(totalFavorites / limitNum);

    res.status(200).json({
      prompts: transformedPrompts,
      totalPages,
      currentPage: pageNum,
      totalPrompts: totalFavorites
    });

  } catch (error) {
    console.error('Error fetching favorites:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
