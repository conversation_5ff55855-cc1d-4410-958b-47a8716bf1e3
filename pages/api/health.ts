import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../lib/mongodb';
import mongoose from 'mongoose';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const healthCheck = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    database: {
      connected: false,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      name: mongoose.connection.name,
    },
    environment: process.env.NODE_ENV,
  };

  try {
    // Test database connection
    await dbConnect();
    
    // Test a simple query
    const testQuery = await mongoose.connection.db.admin().ping();
    
    healthCheck.database.connected = true;
    healthCheck.database.readyState = mongoose.connection.readyState;
    
    res.status(200).json(healthCheck);
  } catch (error) {
    console.error('Health check failed:', error);
    
    healthCheck.status = 'error';
    healthCheck.database.connected = false;
    
    res.status(503).json({
      ...healthCheck,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
